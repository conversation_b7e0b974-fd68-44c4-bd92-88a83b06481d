import{d as ee,r as f,c as M,z as R,o as le,a as p,b as l,f as s,w as a,h as i,g as u,u as y,Q as te,s as B,R as se,S as oe,t as m,F as z,q as F,e as ae,C as ne,v as re,x as c,_ as ie}from"./index-N2UppB4L.js";import{u as ue}from"./bookshelf-Ban4oJs4.js";const de={class:"search-view"},ce={class:"search-content"},pe={class:"search-box"},_e={class:"advanced-search"},ve={class:"search-filters"},fe={class:"filter-group"},me={class:"filter-group"},ge={class:"filter-group"},he={class:"filter-actions"},ke={class:"search-results"},ye={class:"results-header"},Te={class:"results-count"},Se={key:0,class:"books-grid"},Ve=["onClick"],xe={class:"book-cover"},we=["src","alt"],Be={class:"book-info"},Ce=["innerHTML"],be=["innerHTML"],Me={class:"book-meta"},Re={class:"file-type"},ze={class:"file-size"},Fe={key:0,class:"book-progress"},Le={key:1,class:"no-results"},qe={class:"no-results-icon"},Ue={key:2,class:"search-tips"},$e={class:"tips-icon"},He={class:"quick-searches"},Ne={class:"quick-tags"},De=ee({__name:"SearchView",setup(Ee){const L=re(),q=ue(),{books:U,searchBooks:$,setFilter:H}=q,d=f(""),T=f(!1),S=f("relevance"),n=f({fileTypes:[],readStatus:"all",sizeRange:[0,100]}),N=f(["小说","技术","历史","科幻","传记","JavaScript","Python","Vue","React"]),g=M(()=>{let t=U.value||[];if(d.value){const e=d.value.toLowerCase();t=t.filter(r=>r.title.toLowerCase().includes(e)||r.author.toLowerCase().includes(e))}return n.value.fileTypes&&n.value.fileTypes.length>0&&(t=t.filter(e=>n.value.fileTypes.includes(e.fileType))),n.value.readStatus!=="all"&&(t=t.filter(e=>{switch(n.value.readStatus){case"unread":return e.progress===0;case"reading":return e.progress>0&&e.progress<100;case"finished":return e.progress===100;default:return!0}})),t}),D=M(()=>{const t=[...g.value||[]];switch(S.value){case"title":return t.sort((e,r)=>e.title.localeCompare(r.title));case"author":return t.sort((e,r)=>e.author.localeCompare(r.author));case"addTime":return t.sort((e,r)=>r.addTime-e.addTime);case"lastRead":return t.sort((e,r)=>r.lastRead-e.lastRead);default:return t}}),V=()=>{$(d.value)},E=()=>{T.value=!T.value},I=()=>{n.value={fileTypes:[],readStatus:"all",sizeRange:[0,100]},x()},x=()=>{const t={};n.value.fileTypes&&n.value.fileTypes.length>0&&(t.formats=n.value.fileTypes),n.value.readStatus!=="all"&&(t.readingStatus=n.value.readStatus),H(t)},P=t=>{d.value=t,V()},A=t=>{L.push(`/reader/${t.id}`)};R(d,t=>{t.trim()&&V()},{debounce:300}),R(n,()=>{x()},{deep:!0});const C=t=>{if(!d.value)return t;const e=new RegExp(`(${d.value})`,"gi");return t.replace(e,"<mark>$1</mark>")},Q=t=>{if(!t)return"0 B";const e=1024,r=["B","KB","MB","GB"],_=Math.floor(Math.log(t)/Math.log(e));return parseFloat((t/Math.pow(e,_)).toFixed(1))+" "+r[_]},G=t=>`${t} MB`;return le(()=>{console.log("搜索图书页面已加载")}),(t,e)=>{var b;const r=i("el-icon"),_=i("el-button"),J=i("el-input"),h=i("el-checkbox"),K=i("el-checkbox-group"),k=i("el-radio"),O=i("el-radio-group"),X=i("el-slider"),j=i("el-collapse-transition"),v=i("el-option"),W=i("el-select"),Y=i("el-progress"),Z=i("el-tag");return c(),p("div",de,[e[24]||(e[24]=l("div",{class:"search-header"},[l("h1",{class:"page-title"},"搜索图书"),l("p",{class:"page-description"},"快速查找您的图书收藏")],-1)),l("div",ce,[l("div",pe,[s(J,{modelValue:d.value,"onUpdate:modelValue":e[0]||(e[0]=o=>d.value=o),placeholder:"输入书名、作者或关键词...",size:"large",clearable:"",onInput:V},{prefix:a(()=>[s(r,null,{default:a(()=>[s(y(B))]),_:1})]),append:a(()=>[s(_,{onClick:E},{default:a(()=>[s(r,null,{default:a(()=>[s(y(te))]),_:1}),e[5]||(e[5]=u(" 高级搜索 "))]),_:1,__:[5]})]),_:1},8,["modelValue"])]),s(j,null,{default:a(()=>[se(l("div",_e,[l("div",ve,[l("div",fe,[e[10]||(e[10]=l("label",null,"文件格式",-1)),s(K,{modelValue:n.value.fileTypes,"onUpdate:modelValue":e[1]||(e[1]=o=>n.value.fileTypes=o)},{default:a(()=>[s(h,{value:"epub"},{default:a(()=>e[6]||(e[6]=[u("EPUB")])),_:1,__:[6]}),s(h,{value:"pdf"},{default:a(()=>e[7]||(e[7]=[u("PDF")])),_:1,__:[7]}),s(h,{value:"mobi"},{default:a(()=>e[8]||(e[8]=[u("MOBI")])),_:1,__:[8]}),s(h,{value:"txt"},{default:a(()=>e[9]||(e[9]=[u("TXT")])),_:1,__:[9]})]),_:1},8,["modelValue"])]),l("div",me,[e[15]||(e[15]=l("label",null,"阅读状态",-1)),s(O,{modelValue:n.value.readStatus,"onUpdate:modelValue":e[2]||(e[2]=o=>n.value.readStatus=o)},{default:a(()=>[s(k,{value:"all"},{default:a(()=>e[11]||(e[11]=[u("全部")])),_:1,__:[11]}),s(k,{value:"unread"},{default:a(()=>e[12]||(e[12]=[u("未读")])),_:1,__:[12]}),s(k,{value:"reading"},{default:a(()=>e[13]||(e[13]=[u("在读")])),_:1,__:[13]}),s(k,{value:"finished"},{default:a(()=>e[14]||(e[14]=[u("已读")])),_:1,__:[14]})]),_:1},8,["modelValue"])]),l("div",ge,[e[16]||(e[16]=l("label",null,"文件大小",-1)),s(X,{modelValue:n.value.sizeRange,"onUpdate:modelValue":e[3]||(e[3]=o=>n.value.sizeRange=o),range:"",min:0,max:100,"format-tooltip":G},null,8,["modelValue"])]),l("div",he,[s(_,{onClick:I},{default:a(()=>e[17]||(e[17]=[u("重置")])),_:1,__:[17]}),s(_,{type:"primary",onClick:x},{default:a(()=>e[18]||(e[18]=[u("应用筛选")])),_:1,__:[18]})])])],512),[[oe,T.value]])]),_:1}),l("div",ke,[l("div",ye,[l("span",Te," 找到 "+m(((b=g.value)==null?void 0:b.length)||0)+" 本图书 ",1),s(W,{modelValue:S.value,"onUpdate:modelValue":e[4]||(e[4]=o=>S.value=o),placeholder:"排序方式",style:{width:"150px"}},{default:a(()=>[s(v,{label:"相关性",value:"relevance"}),s(v,{label:"书名",value:"title"}),s(v,{label:"作者",value:"author"}),s(v,{label:"添加时间",value:"addTime"}),s(v,{label:"最后阅读",value:"lastRead"})]),_:1},8,["modelValue"])]),g.value&&g.value.length>0?(c(),p("div",Se,[(c(!0),p(z,null,F(D.value,o=>{var w;return c(),p("div",{key:o.id,class:"book-card",onClick:Ie=>A(o)},[l("div",xe,[l("img",{src:o.cover||"/placeholder-book.png",alt:o.title},null,8,we)]),l("div",Be,[l("h3",{innerHTML:C(o.title)},null,8,Ce),l("p",{innerHTML:C(o.author)},null,8,be),l("div",Me,[l("span",Re,m((w=o.fileType)==null?void 0:w.toUpperCase()),1),l("span",ze,m(Q(o.fileSize)),1)]),o.progress>0?(c(),p("div",Fe,[s(Y,{percentage:o.progress,"show-text":!1},null,8,["percentage"]),l("span",null,m(o.progress)+"%",1)])):ae("",!0)])],8,Ve)}),128))])):d.value?(c(),p("div",Le,[l("div",qe,[s(r,null,{default:a(()=>[s(y(B))]),_:1})]),e[19]||(e[19]=l("h3",null,"未找到相关图书",-1)),e[20]||(e[20]=l("p",null,"尝试使用不同的关键词或调整筛选条件",-1))])):(c(),p("div",Ue,[l("div",$e,[s(r,null,{default:a(()=>[s(y(B))]),_:1})]),e[22]||(e[22]=l("h3",null,"开始搜索",-1)),e[23]||(e[23]=l("p",null,"输入书名、作者或关键词来查找图书",-1)),l("div",He,[e[21]||(e[21]=l("h4",null,"快速搜索",-1)),l("div",Ne,[(c(!0),p(z,null,F(N.value||[],o=>(c(),ne(Z,{key:o,onClick:w=>P(o),class:"quick-tag"},{default:a(()=>[u(m(o),1)]),_:2},1032,["onClick"]))),128))])])]))])])])}}}),Qe=ie(De,[["__scopeId","data-v-e8df71a1"]]);export{Qe as default};
