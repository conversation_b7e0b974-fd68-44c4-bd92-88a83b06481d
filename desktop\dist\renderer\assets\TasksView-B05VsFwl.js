import{d as E,r as h,c as u,o as F,a as f,b as t,f as a,w as c,g as m,h as d,u as J,p as q,t as n,F as A,q as K,K as L,x as b,M as p,_ as P}from"./index-BvDCFwwL.js";const R={class:"tasks-view"},j={class:"tasks-header"},G={class:"tasks-content"},H={class:"task-stats"},O={class:"stat-item"},Q={class:"stat-number"},W={class:"stat-item"},X={class:"stat-number"},Y={class:"stat-item"},Z={class:"stat-number"},ee={class:"stat-item"},te={class:"stat-number"},se={class:"task-filters"},oe={class:"task-list"},ae={class:"task-checkbox"},le={class:"task-content"},ne={class:"task-header"},ie={class:"task-title"},de={class:"task-meta"},ce={class:"task-deadline"},re={class:"task-description"},ue={class:"task-progress"},pe={class:"progress-text"},_e={class:"task-actions"},me=E({__name:"TasksView",setup(ve){const v=h("all"),i=h([{id:"1",title:"阅读《JavaScript高级程序设计》第1-3章",description:"深入学习JavaScript基础概念和语法",priority:"高",deadline:Date.now()+864e5*3,progress:75,status:"pending",completed:!1,bookId:"book1"},{id:"2",title:"完成React组件练习",description:"实现一个完整的Todo应用组件",priority:"中",deadline:Date.now()+864e5*7,progress:100,status:"completed",completed:!0,bookId:"book2"},{id:"3",title:"学习Vue3 Composition API",description:"掌握Vue3的新特性和最佳实践",priority:"高",deadline:Date.now()-864e5,progress:30,status:"overdue",completed:!1,bookId:"book3"}]),k=u(()=>i.value.length),T=u(()=>i.value.filter(e=>e.status==="completed").length),w=u(()=>i.value.filter(e=>e.status==="pending").length),y=u(()=>i.value.filter(e=>e.status==="overdue").length),C=u(()=>{switch(v.value){case"pending":return i.value.filter(e=>e.status==="pending");case"completed":return i.value.filter(e=>e.status==="completed");case"overdue":return i.value.filter(e=>e.status==="overdue");default:return i.value}}),V=e=>{console.log("切换到标签:",e)},x=()=>{p.info("创建任务功能开发中...")},$=e=>{e.completed?(e.status="completed",e.progress=100,p.success(`任务"${e.title}"已完成！`)):(e.status="pending",p.info(`任务"${e.title}"已重新激活`))},D=e=>{p.info(`查看任务: ${e.title}`)},I=e=>{e.bookId&&p.info(`继续学习: ${e.title}`)},M=e=>{switch(e){case"高":return"danger";case"中":return"warning";case"低":return"info";default:return"info"}},z=e=>{const o=new Date(e),g=new Date,r=o.getTime()-g.getTime(),l=Math.ceil(r/(1e3*60*60*24));return l<0?`逾期 ${Math.abs(l)} 天`:l===0?"今天截止":l===1?"明天截止":`${l} 天后截止`};return F(()=>{console.log("学习任务页面已加载")}),(e,o)=>{const g=d("el-icon"),r=d("el-button"),l=d("el-tab-pane"),B=d("el-tabs"),N=d("el-checkbox"),S=d("el-tag"),U=d("el-progress");return b(),f("div",R,[t("div",j,[o[2]||(o[2]=t("h1",{class:"page-title"},"学习任务",-1)),a(r,{type:"primary",onClick:x},{default:c(()=>[a(g,null,{default:c(()=>[a(J(q))]),_:1}),o[1]||(o[1]=m(" 创建任务 "))]),_:1,__:[1]})]),t("div",G,[t("div",H,[t("div",O,[t("div",Q,n(k.value),1),o[3]||(o[3]=t("div",{class:"stat-label"},"总任务",-1))]),t("div",W,[t("div",X,n(T.value),1),o[4]||(o[4]=t("div",{class:"stat-label"},"已完成",-1))]),t("div",Y,[t("div",Z,n(w.value),1),o[5]||(o[5]=t("div",{class:"stat-label"},"进行中",-1))]),t("div",ee,[t("div",te,n(y.value),1),o[6]||(o[6]=t("div",{class:"stat-label"},"已逾期",-1))])]),t("div",se,[a(B,{modelValue:v.value,"onUpdate:modelValue":o[0]||(o[0]=s=>v.value=s),onTabChange:V},{default:c(()=>[a(l,{label:"全部",name:"all"}),a(l,{label:"进行中",name:"pending"}),a(l,{label:"已完成",name:"completed"}),a(l,{label:"已逾期",name:"overdue"})]),_:1},8,["modelValue"])]),t("div",oe,[(b(!0),f(A,null,K(C.value,s=>(b(),f("div",{key:s.id,class:L(["task-item",{completed:s.status==="completed",overdue:s.status==="overdue"}])},[t("div",ae,[a(N,{modelValue:s.completed,"onUpdate:modelValue":_=>s.completed=_,onChange:_=>$(s),disabled:s.status==="overdue"},null,8,["modelValue","onUpdate:modelValue","onChange","disabled"])]),t("div",le,[t("div",ne,[t("h3",ie,n(s.title),1),t("div",de,[a(S,{type:M(s.priority),size:"small"},{default:c(()=>[m(n(s.priority),1)]),_:2},1032,["type"]),t("span",ce,n(z(s.deadline)),1)])]),t("p",re,n(s.description),1),t("div",ue,[a(U,{percentage:s.progress,status:s.status==="completed"?"success":void 0,"show-text":!1},null,8,["percentage","status"]),t("span",pe,n(s.progress)+"%",1)]),t("div",_e,[a(r,{size:"small",onClick:_=>D(s)},{default:c(()=>o[7]||(o[7]=[m("查看详情")])),_:2,__:[7]},1032,["onClick"]),a(r,{size:"small",type:"primary",onClick:_=>I(s)},{default:c(()=>[m(n(s.status==="completed"?"重新开始":"继续学习"),1)]),_:2},1032,["onClick"])])])],2))),128))])])])}}}),fe=P(me,[["__scopeId","data-v-d1d66664"]]);export{fe as default};
