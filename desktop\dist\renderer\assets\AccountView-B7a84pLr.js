import{d as t,o as c,a,b as e,f as n,h as p,x as _,_ as d}from"./index-N2UppB4L.js";const i={class:"account-view"},r={class:"account-content"},l=t({__name:"AccountView",setup(m){return c(()=>{console.log("账号设置页面已加载")}),(u,o)=>{const s=p("el-empty");return _(),a("div",i,[o[0]||(o[0]=e("div",{class:"page-header"},[e("h1",{class:"page-title"},"账号设置"),e("p",{class:"page-description"},"管理账号信息")],-1)),e("div",r,[n(s,{description:"账号设置功能开发中..."})])])}}}),v=d(l,[["__scopeId","data-v-832327d3"]]);export{v as default};
