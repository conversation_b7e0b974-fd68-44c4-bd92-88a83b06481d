<!--
  我的书架概览页面
  显示阅读统计、最近阅读、收藏图书等概览信息
-->
<template>
  <div class="bookshelf-overview">
    <!-- 页面头部 -->
    <div class="overview-header">
      <div class="header-left">
        <h1 class="page-title">我的书架</h1>
        <p class="page-subtitle">欢迎回来，继续您的阅读之旅</p>
      </div>
      <div class="header-actions">
        <el-button type="primary" :icon="Plus" @click="handleImport">
          导入图书
        </el-button>
        <el-button :icon="Search" @click="handleSearch">
          搜索图书
        </el-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-section">
      <div class="stats-grid">
        <div class="stat-card">
          <div class="stat-icon">
            <el-icon><Collection /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ stats.total }}</div>
            <div class="stat-label">总图书</div>
          </div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon reading">
            <el-icon><Reading /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ stats.reading }}</div>
            <div class="stat-label">正在阅读</div>
          </div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon finished">
            <el-icon><CircleCheck /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ stats.finished }}</div>
            <div class="stat-label">已完成</div>
          </div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon favorite">
            <el-icon><Star /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ stats.favorites }}</div>
            <div class="stat-label">收藏图书</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 快速操作 -->
    <div class="quick-actions-section">
      <h2 class="section-title">快速操作</h2>
      <div class="quick-actions-grid">
        <div class="action-card" @click="handleViewLibrary">
          <div class="action-icon">
            <el-icon><Grid /></el-icon>
          </div>
          <div class="action-content">
            <div class="action-title">浏览图书</div>
            <div class="action-desc">查看所有图书列表</div>
          </div>
        </div>
        
        <div class="action-card" @click="handleImport">
          <div class="action-icon">
            <el-icon><Upload /></el-icon>
          </div>
          <div class="action-content">
            <div class="action-title">导入图书</div>
            <div class="action-desc">添加新的电子书</div>
          </div>
        </div>
        
        <div class="action-card" @click="handleSearch">
          <div class="action-icon">
            <el-icon><Search /></el-icon>
          </div>
          <div class="action-content">
            <div class="action-title">搜索图书</div>
            <div class="action-desc">快速查找图书</div>
          </div>
        </div>
        
        <div class="action-card" @click="handleBookmarks">
          <div class="action-icon">
            <el-icon><Star /></el-icon>
          </div>
          <div class="action-content">
            <div class="action-title">书签笔记</div>
            <div class="action-desc">管理阅读标记</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 最近阅读 -->
    <div class="recent-section" v-if="recentBooks.length > 0">
      <div class="section-header">
        <h2 class="section-title">最近阅读</h2>
        <el-button text @click="handleViewLibrary">查看全部</el-button>
      </div>
      <div class="recent-books-grid">
        <div 
          v-for="book in recentBooks" 
          :key="book.id"
          class="book-card"
          @click="handleBookClick(book)"
        >
          <div class="book-cover">
            <img 
              :src="book.coverPath || placeholderImage" 
              :alt="book.title"
              @error="handleImageError"
            />
          </div>
          <div class="book-info">
            <div class="book-title">{{ book.title }}</div>
            <div class="book-author">{{ book.author || '未知作者' }}</div>
            <div class="book-progress" v-if="book.readingProgress">
              <el-progress 
                :percentage="Math.round(book.readingProgress * 100)" 
                :show-text="false"
                :stroke-width="4"
              />
              <span class="progress-text">{{ Math.round(book.readingProgress * 100) }}%</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 收藏图书 -->
    <div class="favorites-section" v-if="favoriteBooks.length > 0">
      <div class="section-header">
        <h2 class="section-title">收藏图书</h2>
        <el-button text @click="handleViewFavorites">查看全部</el-button>
      </div>
      <div class="favorite-books-grid">
        <div 
          v-for="book in favoriteBooks" 
          :key="book.id"
          class="book-card"
          @click="handleBookClick(book)"
        >
          <div class="book-cover">
            <img 
              :src="book.coverPath || placeholderImage" 
              :alt="book.title"
              @error="handleImageError"
            />
            <div class="favorite-badge">
              <el-icon><Star /></el-icon>
            </div>
          </div>
          <div class="book-info">
            <div class="book-title">{{ book.title }}</div>
            <div class="book-author">{{ book.author || '未知作者' }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <div class="empty-state" v-if="stats.total === 0">
      <div class="empty-icon">
        <el-icon><Collection /></el-icon>
      </div>
      <div class="empty-title">书架还是空的</div>
      <div class="empty-desc">开始导入您的第一本电子书吧</div>
      <el-button type="primary" :icon="Plus" @click="handleImport">
        导入图书
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { 
  Plus, Search, Collection, Reading, CircleCheck, Star, 
  Grid, Upload 
} from '@element-plus/icons-vue'
import { useBookshelfStore } from '@renderer/store/bookshelf'
import placeholderImage from '@renderer/assets/placeholder-book.svg'
import type { Book } from '@shared/types/database'

// 路由和状态管理
const router = useRouter()
const bookshelfStore = useBookshelfStore()

// 响应式数据
const loading = ref(false)

// 计算属性
const stats = computed(() => {
  const books = bookshelfStore.books
  return {
    total: books.length,
    reading: books.filter(book => book.readingStatus === 'reading').length,
    finished: books.filter(book => book.readingStatus === 'finished').length,
    favorites: books.filter(book => book.isFavorite).length
  }
})

const recentBooks = computed(() => {
  return bookshelfStore.books
    .filter(book => book.lastReadAt)
    .sort((a, b) => new Date(b.lastReadAt!).getTime() - new Date(a.lastReadAt!).getTime())
    .slice(0, 6)
})

const favoriteBooks = computed(() => {
  return bookshelfStore.books
    .filter(book => book.isFavorite)
    .slice(0, 6)
})

// 方法
const handleViewLibrary = () => {
  router.push('/library')
}

const handleImport = () => {
  router.push('/import')
}

const handleSearch = () => {
  router.push('/search')
}

const handleBookmarks = () => {
  router.push('/bookmark')
}

const handleViewFavorites = () => {
  router.push('/library?filter=favorites')
}

const handleBookClick = (book: Book) => {
  router.push(`/reader/${book.id}`)
}

const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement
  img.src = placeholderImage
}

// 生命周期
onMounted(async () => {
  loading.value = true
  try {
    await bookshelfStore.loadBooks()
  } catch (error) {
    console.error('加载图书数据失败:', error)
  } finally {
    loading.value = false
  }
})
</script>

<style scoped>
.bookshelf-overview {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.overview-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 32px;
}

.header-left {
  flex: 1;
}

.page-title {
  font-size: 32px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 8px 0;
}

.page-subtitle {
  font-size: 16px;
  color: var(--text-secondary);
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.stats-section {
  margin-bottom: 40px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.stat-card {
  background: var(--bg-secondary);
  border-radius: 12px;
  padding: 24px;
  display: flex;
  align-items: center;
  gap: 16px;
  border: 1px solid var(--border-light);
  transition: all 0.2s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--primary-color);
  color: white;
  font-size: 24px;
}

.stat-icon.reading {
  background: #67c23a;
}

.stat-icon.finished {
  background: #409eff;
}

.stat-icon.favorite {
  background: #f56c6c;
}

.stat-number {
  font-size: 28px;
  font-weight: 600;
  color: var(--text-primary);
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: var(--text-secondary);
  margin-top: 4px;
}

.quick-actions-section,
.recent-section,
.favorites-section {
  margin-bottom: 40px;
}

.section-title {
  font-size: 20px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 20px 0;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.quick-actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
}

.action-card {
  background: var(--bg-secondary);
  border-radius: 12px;
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 16px;
  border: 1px solid var(--border-light);
  cursor: pointer;
  transition: all 0.2s ease;
}

.action-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-color: var(--primary-color);
}

.action-icon {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--primary-color);
  color: white;
  font-size: 20px;
}

.action-title {
  font-size: 16px;
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: 4px;
}

.action-desc {
  font-size: 14px;
  color: var(--text-secondary);
}

.recent-books-grid,
.favorite-books-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
  gap: 20px;
}

.book-card {
  cursor: pointer;
  transition: transform 0.2s ease;
}

.book-card:hover {
  transform: translateY(-4px);
}

.book-cover {
  position: relative;
  aspect-ratio: 3/4;
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.book-cover img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.favorite-badge {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: #f56c6c;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
}

.book-title {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.book-author {
  font-size: 12px;
  color: var(--text-secondary);
  margin-bottom: 8px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.book-progress {
  display: flex;
  align-items: center;
  gap: 8px;
}

.progress-text {
  font-size: 12px;
  color: var(--text-secondary);
  min-width: 32px;
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
}

.empty-icon {
  font-size: 64px;
  color: var(--text-tertiary);
  margin-bottom: 16px;
}

.empty-title {
  font-size: 20px;
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: 8px;
}

.empty-desc {
  font-size: 14px;
  color: var(--text-secondary);
  margin-bottom: 24px;
}
</style>
