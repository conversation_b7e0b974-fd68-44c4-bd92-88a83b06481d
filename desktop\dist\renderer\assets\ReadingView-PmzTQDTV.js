import{d as t,o as a,a as n,b as e,f as c,h as d,x as i,_ as p}from"./index-BvDCFwwL.js";const r={class:"reading-view"},_={class:"reading-content"},l=t({__name:"ReadingView",setup(m){return a(()=>{console.log("阅读设置页面已加载")}),(g,s)=>{const o=d("el-empty");return i(),n("div",r,[s[0]||(s[0]=e("div",{class:"page-header"},[e("h1",{class:"page-title"},"阅读设置"),e("p",{class:"page-description"},"阅读体验配置")],-1)),e("div",_,[c(o,{description:"阅读设置功能开发中..."})])])}}}),v=p(l,[["__scopeId","data-v-c727314c"]]);export{v as default};
