<template>
  <div id="app" :class="appClasses">
    <!-- 自定义标题栏 -->
    <div class="title-bar" v-if="!isFullscreen && !isReaderMode">
      <div class="title-bar-content">
        <!-- 移动端菜单按钮 -->
        <div class="mobile-menu-btn" v-if="isMobile && !isReaderMode">
          <el-button
            link
            @click="toggleMobileSidebar"
            class="hamburger-btn"
            title="打开菜单"
          >
            <el-icon><Menu /></el-icon>
          </el-button>
        </div>

        <!-- 应用标题和图标 -->
        <div class="app-title">
          <div class="app-logo">
            <el-icon class="app-icon"><Reading /></el-icon>
          </div>
          <div class="app-info">
            <span class="app-name">Yu Reader</span>
            <span class="app-subtitle">智能电子书阅读器</span>
          </div>
        </div>

        <!-- 面包屑导航 -->
        <div class="breadcrumb-nav">
          <BreadcrumbNavigation
            :auto-generate="true"
            :show-actions="false"
            :compact="true"
            :max-items="4"
            @breadcrumb-click="handleBreadcrumbClick"
          />
        </div>

        <!-- 窗口控制按钮 -->
        <div class="window-controls">
          <el-button
            link
            size="small"
            @click="minimizeWindow"
            class="window-control-btn"
            title="最小化"
          >
            <el-icon><Minus /></el-icon>
          </el-button>
          <el-button
            link
            size="small"
            @click="maximizeWindow"
            class="window-control-btn"
            title="最大化"
          >
            <el-icon><FullScreen /></el-icon>
          </el-button>
          <el-button
            link
            size="small"
            @click="closeWindow"
            class="window-control-btn close-btn"
            title="关闭"
          >
            <el-icon><Close /></el-icon>
          </el-button>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 移动端侧边栏遮罩 -->
      <div
        class="sidebar-overlay"
        v-if="isMobile && !isReaderMode && sidebarVisible"
        :class="{ 'show': sidebarVisible }"
        @click="toggleMobileSidebar"
      ></div>

      <!-- 现代化侧边栏导航 -->
      <div
        class="sidebar"
        v-if="!isReaderMode"
        :class="{
          'collapsed': sidebarCollapsed,
          'show': isMobile && sidebarVisible
        }"
      >
        <!-- 侧边栏头部 -->
        <div class="sidebar-header">
          <div class="sidebar-logo" v-if="!sidebarCollapsed">
            <el-icon class="logo-icon"><Reading /></el-icon>
            <div class="logo-text">
              <span class="logo-title">Yu Reader</span>
              <span class="logo-subtitle">阅读管理</span>
            </div>
          </div>
          <div class="sidebar-logo-collapsed" v-else>
            <el-icon class="logo-icon"><Reading /></el-icon>
          </div>

          <!-- 折叠按钮 -->
          <el-button
            link
            @click="toggleSidebar"
            class="sidebar-toggle-btn"
            :title="sidebarCollapsed ? '展开侧边栏' : '折叠侧边栏'"
          >
            <el-icon>
              <component :is="sidebarCollapsed ? 'Expand' : 'Fold'" />
            </el-icon>
          </el-button>
        </div>

        <!-- 导航菜单 -->
        <div class="sidebar-content">
          <NavigationMenu
            :collapsed="sidebarCollapsed"
            :menu-items="navigationMenuItems"
            :quick-actions="navigationQuickActions"
            @menu-click="handleMenuClick"
            @submenu-click="handleSubmenuClick"
            @action-click="handleActionClick"
          />
        </div>

        <!-- 侧边栏底部 -->
        <div class="sidebar-footer">
          <!-- 主题切换按钮 -->
          <div class="theme-switcher">
            <el-button
              link
              @click="toggleTheme"
              class="theme-btn"
              :title="sidebarCollapsed ? `切换主题 (当前: ${currentThemeName})` : undefined"
            >
              <el-icon>
                <component :is="currentThemeIcon" />
              </el-icon>
              <span v-if="!sidebarCollapsed">{{ currentThemeName }}</span>
            </el-button>
          </div>
        </div>
      </div>

      <!-- 主内容区域 -->
      <div class="content-area" :class="{ 'full-width': isReaderMode, 'sidebar-collapsed': sidebarCollapsed }">
        <!-- 内容头部工具栏 -->
        <div class="content-header" v-if="!isReaderMode">
          <div class="content-title">
            <h1>{{ currentPageTitle }}</h1>
          </div>
          <div class="content-actions">
            <!-- 这里可以放置页面特定的操作按钮 -->
          </div>
        </div>

        <!-- 路由视图 -->
        <div class="content-body">
          <router-view />
        </div>
      </div>
    </div>

    <!-- 状态栏 -->
    <StatusBar
      v-if="!isFullscreen && !isReaderMode"
      :app-status="appStatus"
      :network-status="networkStatus"
      :current-theme="currentTheme?.id || 'light'"
      :progress="progressInfo"
      :left-items="statusLeftItems"
      :right-items="statusRightItems"
      @theme-toggle="toggleTheme"
      @settings-click="handleSettingsClick"
      @item-click="handleStatusItemClick"
    />

    <!-- 全局加载指示器 -->
    <div
      v-loading="globalLoading"
      element-loading-text="加载中..."
      element-loading-background="rgba(0, 0, 0, 0.8)"
      class="global-loading"
    ></div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import {
  Reading,
  Minus,
  FullScreen,
  Close,
  Collection,
  Star,
  EditPen,
  Setting,
  Fold,
  Expand,
  User,
  Sunny,
  Moon,
  Menu
} from '@element-plus/icons-vue'
import { useAppStore } from './store/app'
import { useThemeStore } from './store/theme'
import { getBreadcrumbs, getMainMenuItems } from './router'
import NavigationMenu from './components/NavigationMenu.vue'
import BreadcrumbNavigation from './components/BreadcrumbNavigation.vue'
import StatusBar from './components/StatusBar.vue'
import { mainNavigationItems, quickActions } from './config/navigation'
import type { MenuItem, QuickAction } from './config/navigation'
import type { BreadcrumbItem } from './components/BreadcrumbNavigation.vue'
import type { StatusItem, ProgressInfo } from './components/StatusBar.vue'
import { useWindowManager } from './composables/useWindowManager'

// 状态管理
const appStore = useAppStore()
const themeStore = useThemeStore()
const route = useRoute()
const router = useRouter()

// 窗口管理
const {
  windowWidth,
  windowHeight,
  isMaximized,
  isFullscreen,
  isMobile,
  isTablet,
  isDesktop,
  currentBreakpoint,
  minimizeWindow,
  maximizeWindow,
  toggleFullscreen,
  closeWindow,
  getResponsiveClasses
} = useWindowManager()

// 响应式数据
const globalLoading = ref(false)
const networkStatus = ref('已连接')
const sidebarVisible = ref(false)
const progressInfo = ref<ProgressInfo>({
  visible: false,
  percentage: 0,
  text: '',
  status: ''
})

// 计算属性
const isReaderMode = computed(() => route.path.startsWith('/reader'))
const sidebarCollapsed = computed(() => {
  // 在移动设备上默认折叠侧边栏
  if (isMobile.value) return true
  return appStore.sidebarCollapsed
})
const isDarkMode = computed(() => appStore.isDarkMode)
const currentTheme = computed(() => appStore.currentTheme)

// 当前主题图标
const currentThemeIcon = computed(() => {
  const themeId = themeStore.currentThemeId
  switch (themeId) {
    case 'dark':
    case 'night':
      return 'Sunny'
    case 'eye-care':
    case 'eye-care-warm':
      return 'View'
    case 'high-contrast':
      return 'Monitor'
    case 'natural':
      return 'Grape'
    case 'auto':
      return 'MagicStick'
    default:
      return 'Moon'
  }
})

// 当前主题名称
const currentThemeName = computed(() => {
  const themeId = themeStore.currentThemeId
  switch (themeId) {
    case 'light':
      return '明亮'
    case 'dark':
      return '深色'
    case 'auto':
      return '自动'
    case 'eye-care':
      return '护眼'
    case 'eye-care-warm':
      return '护眼暖色'
    case 'high-contrast':
      return '高对比度'
    case 'night':
      return '夜间'
    case 'natural':
      return '自然'
    default:
      return '未知'
  }
})

// 响应式类名
const appClasses = computed(() => {
  return [
    'yu-reader-app',
    {
      'fullscreen': isFullscreen.value,
      'reader-mode': isReaderMode.value,
      'mobile': isMobile.value,
      'tablet': isTablet.value,
      'desktop': isDesktop.value,
      'sidebar-collapsed': sidebarCollapsed.value
    },
    `breakpoint-${currentBreakpoint.value}`
  ]
})

// 面包屑导航事件处理
const handleBreadcrumbClick = (item: BreadcrumbItem, index: number) => {
  console.log('面包屑点击:', item.title, index)
  // 这里可以添加面包屑点击的特殊处理逻辑
}

// 导航菜单数据
const navigationMenuItems = computed(() => mainNavigationItems)
const navigationQuickActions = computed(() => {
  return quickActions.map(action => ({
    ...action,
    action: () => handleQuickAction(action)
  }))
})

// 当前页面标题
const currentPageTitle = computed(() => {
  return route.meta?.title || '未知页面'
})

// 应用状态
const appStatus = computed(() => {
  if (globalLoading.value) return '加载中'
  if (isReaderMode.value) return '阅读模式'
  if (progressInfo.value.visible) return '处理中'
  return '就绪'
})

// 状态栏项目
const statusLeftItems = computed<StatusItem[]>(() => [
  // 可以在这里添加自定义的左侧状态项
])

const statusRightItems = computed<StatusItem[]>(() => [
  // 可以在这里添加自定义的右侧状态项
])

// 窗口控制方法已移至 useWindowManager

// 侧边栏控制
const toggleSidebar = () => {
  if (isMobile.value) {
    toggleMobileSidebar()
  } else {
    appStore.toggleSidebar()
  }
}

const toggleMobileSidebar = () => {
  sidebarVisible.value = !sidebarVisible.value
}

const closeMobileSidebar = () => {
  if (isMobile.value) {
    sidebarVisible.value = false
  }
}

// 主题切换
const toggleTheme = async () => {
  const currentThemeId = themeStore.currentThemeId

  // 定义主题切换顺序
  const themeOrder = ['light', 'dark', 'eye-care', 'eye-care-warm', 'high-contrast', 'night', 'natural']
  const currentIndex = themeOrder.indexOf(currentThemeId)
  const nextIndex = (currentIndex + 1) % themeOrder.length
  const newThemeId = themeOrder[nextIndex]

  await themeStore.setTheme(newThemeId)
}

// 设置特定主题
const setTheme = async (themeId: string) => {
  try {
    await themeStore.setTheme(themeId)
    console.log(`主题已切换到: ${themeId}`)
  } catch (error) {
    console.error('切换主题失败:', error)
  }
}

// 导航菜单事件处理
const handleMenuClick = (item: MenuItem) => {
  console.log('菜单点击:', item.title)
  // 这里可以添加菜单点击的特殊处理逻辑
}

const handleSubmenuClick = (item: MenuItem) => {
  console.log('子菜单点击:', item.title)
  // 这里可以添加子菜单点击的特殊处理逻辑
}

const handleActionClick = (action: QuickAction) => {
  console.log('快速操作点击:', action.title)
  // 这里可以添加快速操作的特殊处理逻辑
}

const handleQuickAction = (action: QuickAction) => {
  switch (action.id) {
    case 'import-book':
      // 触发导入图书功能
      router.push('/import')
      break
    case 'search-global':
      // 触发全局搜索
      // 这里可以打开搜索对话框或跳转到搜索页面
      break
    case 'recent-books':
      // 显示最近阅读的书籍
      // 这里可以打开最近阅读列表
      break
    default:
      console.log('未知的快速操作:', action.id)
  }
}

// 检查路由是否激活
const isActiveRoute = (path: string) => {
  return route.path.startsWith(path)
}

// 状态栏事件处理
const handleSettingsClick = () => {
  router.push('/settings')
}

const handleStatusItemClick = (item: StatusItem) => {
  console.log('状态栏项目点击:', item.id)
  // 这里可以添加状态栏项目点击的特殊处理逻辑
}

// 设置进度信息
const setProgress = (visible: boolean, percentage = 0, text = '', status: ProgressInfo['status'] = '') => {
  progressInfo.value = {
    visible,
    percentage,
    text,
    status
  }
}

// 更新网络状态
const updateNetworkStatus = () => {
  // 检查网络连接状态
  if (navigator.onLine) {
    networkStatus.value = '已连接'
  } else {
    networkStatus.value = '离线'
  }
}

// 监听路由变化
watch(route, (newRoute) => {
  appStore.updateRoute(newRoute.path)
  // 在移动端路由变化时自动关闭侧边栏
  closeMobileSidebar()
}, { immediate: true })

// 监听窗口大小变化，在桌面端自动关闭移动端侧边栏
watch(isMobile, (mobile) => {
  if (!mobile) {
    sidebarVisible.value = false
  }
})

// 组件挂载时的初始化
onMounted(async () => {
  try {
    globalLoading.value = true

    // 初始化应用设置
    await appStore.initializeApp()

    // 初始化网络状态监听
    updateNetworkStatus()
    window.addEventListener('online', updateNetworkStatus)
    window.addEventListener('offline', updateNetworkStatus)

    // 监听菜单栏主题切换消息
    if (window.electronAPI?.theme?.onChange) {
      window.electronAPI.theme.onChange((themeId: string) => {
        console.log(`收到菜单主题切换消息: ${themeId}`)
        setTheme(themeId)
      })
    }

    console.log('Yu Reader 应用初始化完成')
  } catch (error) {
    console.error('应用初始化失败:', error)
  } finally {
    globalLoading.value = false
  }
})
</script>

<style scoped>
/* 应用主容器 */
.yu-reader-app {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: var(--bg-primary);
  color: var(--text-primary);
  transition: all var(--transition-normal);
}

.yu-reader-app.fullscreen {
  height: 100vh;
}

.yu-reader-app.reader-mode {
  background-color: var(--reader-background);
  color: var(--reader-text);
}

/* 标题栏样式 */
.title-bar {
  height: 48px;
  background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
  border-bottom: 1px solid var(--border-base);
  -webkit-app-region: drag;
  user-select: none;
  backdrop-filter: blur(10px);
  z-index: 1000;
}

.title-bar-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
  padding: 0 16px;
}

.app-title {
  display: flex;
  align-items: center;
  gap: 12px;
  -webkit-app-region: no-drag;
}

.app-logo {
  width: 32px;
  height: 32px;
  background: linear-gradient(135deg, var(--color-primary-500) 0%, var(--color-primary-600) 100%);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: var(--shadow-sm);
}

.app-icon {
  color: white;
  font-size: 18px;
}

.app-info {
  display: flex;
  flex-direction: column;
}

.app-name {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  line-height: 1.2;
}

.app-subtitle {
  font-size: 12px;
  color: var(--text-secondary);
  line-height: 1.2;
}

/* 移动端菜单按钮 */
.mobile-menu-btn {
  display: flex;
  align-items: center;
  -webkit-app-region: no-drag;
}

.hamburger-btn {
  width: 40px;
  height: 40px;
  padding: 0;
  border-radius: 8px;
  transition: all var(--transition-fast);
}

.hamburger-btn:hover {
  background-color: var(--bg-tertiary);
  transform: scale(1.05);
}

/* 面包屑导航 */
.breadcrumb-nav {
  flex: 1;
  display: flex;
  justify-content: center;
  -webkit-app-region: no-drag;
}

.breadcrumb-icon {
  margin-right: 4px;
  font-size: 14px;
}

/* 窗口控制按钮 */
.window-controls {
  display: flex;
  -webkit-app-region: no-drag;
}

.window-control-btn {
  width: 36px;
  height: 36px;
  padding: 0;
  border-radius: 6px;
  margin-left: 4px;
  transition: all var(--transition-fast);
}

.window-control-btn:hover {
  background-color: var(--bg-tertiary);
  transform: scale(1.05);
}

.close-btn:hover {
  background-color: #ef4444;
  color: white;
}

/* 主内容区域 */
.main-content {
  flex: 1;
  display: flex;
  overflow: hidden;
  position: relative;
}

/* 现代化侧边栏 */
.sidebar {
  width: 280px;
  background: linear-gradient(180deg, var(--bg-secondary) 0%, var(--bg-primary) 100%);
  border-right: 1px solid var(--border-base);
  display: flex;
  flex-direction: column;
  transition: all var(--transition-normal);
  box-shadow: var(--shadow-lg);
  z-index: 100;
}

.sidebar.collapsed {
  width: 80px;
}

/* 侧边栏头部 */
.sidebar-header {
  padding: 20px 16px;
  border-bottom: 1px solid var(--border-light);
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: var(--bg-elevated);
}

.sidebar-logo {
  display: flex;
  align-items: center;
  gap: 12px;
}

.sidebar-logo-collapsed {
  display: flex;
  justify-content: center;
  width: 100%;
}

.logo-icon {
  width: 36px;
  height: 36px;
  background: linear-gradient(135deg, var(--color-primary-500) 0%, var(--color-primary-600) 100%);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
  box-shadow: var(--shadow-md);
}

.logo-text {
  display: flex;
  flex-direction: column;
}

.logo-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  line-height: 1.2;
}

.logo-subtitle {
  font-size: 12px;
  color: var(--text-secondary);
  line-height: 1.2;
}

.sidebar-toggle-btn {
  width: 32px;
  height: 32px;
  padding: 0;
  border-radius: 6px;
  transition: all var(--transition-fast);
}

.sidebar-toggle-btn:hover {
  background-color: var(--bg-tertiary);
  transform: scale(1.1);
}

/* 侧边栏内容 */
.sidebar-content {
  flex: 1;
  padding: 16px 12px;
  overflow-y: auto;
}

.nav-section {
  margin-bottom: 24px;
}

.nav-section-title {
  font-size: 12px;
  font-weight: 600;
  color: var(--text-tertiary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 8px;
  padding: 0 12px;
}

.nav-menu {
  list-style: none;
  padding: 0;
  margin: 0;
}

.nav-item {
  margin-bottom: 4px;
}

.nav-link {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  border-radius: 10px;
  text-decoration: none;
  color: var(--text-secondary);
  transition: all var(--transition-fast);
  position: relative;
  overflow: hidden;
}

.nav-link:hover {
  background-color: var(--bg-tertiary);
  color: var(--text-primary);
  transform: translateX(4px);
}

.nav-link.active {
  background: linear-gradient(135deg, var(--color-primary-500) 0%, var(--color-primary-600) 100%);
  color: white;
  box-shadow: var(--shadow-md);
}

.nav-icon {
  font-size: 18px;
  min-width: 18px;
  transition: all var(--transition-fast);
}

.nav-text {
  font-size: 14px;
  font-weight: 500;
  transition: all var(--transition-fast);
}

.nav-indicator {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  width: 6px;
  height: 6px;
  background-color: white;
  border-radius: 50%;
  opacity: 0.8;
}

/* 侧边栏底部 */
.sidebar-footer {
  padding: 16px 12px;
  border-top: 1px solid var(--border-light);
  background: var(--bg-elevated);
}

.theme-switcher {
  margin-bottom: 8px;
}

.theme-btn {
  width: 100%;
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  border-radius: 10px;
  transition: all var(--transition-fast);
  justify-content: flex-start;
}

.theme-btn:hover {
  background-color: var(--bg-tertiary);
}

/* 主内容区域 */
.content-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  transition: all var(--transition-normal);
  margin-left: 0;
}

.content-area.sidebar-collapsed {
  margin-left: 0;
}

.content-area.full-width {
  margin-left: 0;
}

/* 内容头部 */
.content-header {
  padding: 10px 10px;
  border-bottom: 1px solid var(--border-light);
  background: var(--bg-elevated);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.content-title h1 {
  font-size: 24px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.content-actions {
  display: flex;
  gap: 12px;
}

/* 内容主体 */
.content-body {
  flex: 1;
  overflow: auto;
  background: #ffffff00 !important;

}

/* 状态栏 */
.status-bar {
  height: 28px;
  background: var(--bg-secondary);
  border-top: 1px solid var(--border-base);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  font-size: 12px;
  color: var(--text-secondary);
}

.status-left,
.status-right {
  display: flex;
  gap: 16px;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

/* 响应式设计 */

/* 移动设备 (< 768px) */
.yu-reader-app.mobile {
  /* 标题栏调整 */
  .title-bar {
    height: 44px;
    padding: 0 12px;
  }

  .app-subtitle {
    display: none;
  }

  .breadcrumb-nav {
    display: none;
  }

  /* 侧边栏移动端处理 */
  .sidebar {
    position: fixed;
    left: 0;
    top: 44px;
    height: calc(100vh - 44px);
    z-index: 200;
    width: 280px;
    transform: translateX(-100%);
    transition: transform var(--transition-normal);
  }

  .sidebar.show {
    transform: translateX(0);
  }

  .sidebar-overlay {
    position: fixed;
    top: 44px;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 199;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal);
  }

  .sidebar-overlay.show {
    opacity: 1;
    visibility: visible;
  }

  /* 内容区域调整 */
  .content-area {
    margin-left: 0 !important;
  }

  /* 状态栏调整 */
  .status-bar {
    height: 24px;
    padding: 0 8px;
    font-size: 11px;
  }
}

/* 平板设备 (768px - 1024px) */
.yu-reader-app.tablet {
  .sidebar {
    width: 240px;
  }

  .sidebar.collapsed {
    width: 60px;
  }
}

/* 桌面设备 (>= 1024px) */
.yu-reader-app.desktop {
  .sidebar {
    width: 280px;
  }

  .sidebar.collapsed {
    width: 80px;
  }
}

/* 断点特定样式 */
.breakpoint-xs {
  .title-bar {
    height: 40px;
  }

  .sidebar {
    width: 100vw;
  }
}

.breakpoint-sm {
  .sidebar {
    width: 280px;
  }
}

.breakpoint-md {
  .sidebar {
    width: 260px;
  }
}

.breakpoint-lg {
  .sidebar {
    width: 280px;
  }
}

.breakpoint-xl,
.breakpoint-2xl {
  .sidebar {
    width: 320px;
  }

  .sidebar.collapsed {
    width: 80px;
  }
}

/* 动画效果 */
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.nav-link,
.theme-btn {
  animation: slideIn 0.3s ease-out;
}

/* 滚动条样式 */
.sidebar-content::-webkit-scrollbar {
  width: 4px;
}

.sidebar-content::-webkit-scrollbar-track {
  background: transparent;
}

.sidebar-content::-webkit-scrollbar-thumb {
  background: var(--border-dark);
  border-radius: 2px;
}

.sidebar-content::-webkit-scrollbar-thumb:hover {
  background: var(--border-darker);
}

/* 全局加载指示器 */
.global-loading {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 9999;
}
</style>
