<!--
  导航菜单组件
  支持5大功能模块的导航，包括图标、折叠展开、活跃状态等功能
-->
<template>
  <nav class="navigation-menu">
    <!-- 主要功能模块 -->
    <div class="nav-section">
      <div class="nav-section-title" v-if="!collapsed">主要功能</div>
      <ul class="nav-menu">
        <li 
          v-for="item in mainMenuItems" 
          :key="item.path"
          class="nav-item"
        >
          <router-link 
            :to="item.path" 
            class="nav-link"
            :class="{ 'active': isActiveRoute(item.path) }"
            :title="collapsed ? item.title : undefined"
            @click="handleMenuClick(item)"
          >
            <div class="nav-icon-wrapper">
              <el-icon class="nav-icon">
                <component :is="item.icon" />
              </el-icon>
              <!-- 活跃指示器 -->
              <div class="nav-indicator" v-if="isActiveRoute(item.path)"></div>
            </div>
            
            <div class="nav-content" v-if="!collapsed">
              <span class="nav-text">{{ item.title }}</span>
              <span class="nav-description" v-if="item.description">{{ item.description }}</span>
            </div>

            <!-- 子菜单指示器 -->
            <el-icon 
              v-if="item.children && item.children.length > 0 && !collapsed" 
              class="nav-arrow"
              :class="{ 'expanded': expandedItems.includes(item.path) }"
            >
              <ArrowRight />
            </el-icon>

            <!-- 折叠状态下的工具提示 -->
            <div class="nav-tooltip" v-if="collapsed">
              <div class="tooltip-content">
                <div class="tooltip-title">{{ item.title }}</div>
                <div class="tooltip-description" v-if="item.description">{{ item.description }}</div>
                <div class="tooltip-children" v-if="item.children && item.children.length > 0">
                  <div class="tooltip-child" v-for="child in item.children" :key="child.path">
                    {{ child.title }}
                  </div>
                </div>
              </div>
              <div class="tooltip-arrow"></div>
            </div>
          </router-link>

          <!-- 子菜单 -->
          <transition name="submenu">
            <ul 
              v-if="item.children && item.children.length > 0 && !collapsed && expandedItems.includes(item.path)"
              class="nav-submenu"
            >
              <li 
                v-for="child in item.children" 
                :key="child.path"
                class="nav-subitem"
              >
                <router-link 
                  :to="child.path" 
                  class="nav-sublink"
                  :class="{ 'active': isActiveRoute(child.path) }"
                  @click="handleSubmenuClick(child)"
                >
                  <el-icon class="nav-subicon" v-if="child.icon">
                    <component :is="child.icon" />
                  </el-icon>
                  <span class="nav-subtext">{{ child.title }}</span>
                </router-link>
              </li>
            </ul>
          </transition>
        </li>
      </ul>
    </div>

    <!-- 快速操作 -->
    <div class="nav-section" v-if="quickActions.length > 0">
      <div class="nav-section-title" v-if="!collapsed">快速操作</div>
      <ul class="nav-menu">
        <li 
          v-for="action in quickActions" 
          :key="action.id"
          class="nav-item"
        >
          <button 
            class="nav-action"
            :title="collapsed ? action.title : undefined"
            @click="handleActionClick(action)"
          >
            <div class="nav-icon-wrapper">
              <el-icon class="nav-icon">
                <component :is="action.icon" />
              </el-icon>
            </div>
            
            <div class="nav-content" v-if="!collapsed">
              <span class="nav-text">{{ action.title }}</span>
              <span class="nav-description" v-if="action.description">{{ action.description }}</span>
            </div>

            <!-- 快捷键提示 -->
            <span class="nav-shortcut" v-if="action.shortcut && !collapsed">
              {{ action.shortcut }}
            </span>
          </button>
        </li>
      </ul>
    </div>
  </nav>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useRoute } from 'vue-router'
import { ArrowRight } from '@element-plus/icons-vue'

// 接口定义
interface MenuItem {
  path: string
  title: string
  description?: string
  icon: string
  children?: MenuItem[]
}

interface QuickAction {
  id: string
  title: string
  description?: string
  icon: string
  shortcut?: string
  action: () => void
}

// Props
interface Props {
  collapsed?: boolean
  menuItems?: MenuItem[]
  quickActions?: QuickAction[]
}

const props = withDefaults(defineProps<Props>(), {
  collapsed: false,
  menuItems: () => [],
  quickActions: () => []
})

// Emits
const emit = defineEmits<{
  menuClick: [item: MenuItem]
  submenuClick: [item: MenuItem]
  actionClick: [action: QuickAction]
}>()

// 状态
const route = useRoute()
const expandedItems = ref<string[]>([])

// 计算属性
const mainMenuItems = computed(() => props.menuItems)

// 方法
const isActiveRoute = (path: string) => {
  return route.path.startsWith(path)
}

const handleMenuClick = (item: MenuItem) => {
  // 切换子菜单展开状态
  if (item.children && item.children.length > 0 && !props.collapsed) {
    const index = expandedItems.value.indexOf(item.path)
    if (index > -1) {
      expandedItems.value.splice(index, 1)
    } else {
      expandedItems.value.push(item.path)
    }
  }
  
  emit('menuClick', item)
}

const handleSubmenuClick = (item: MenuItem) => {
  emit('submenuClick', item)
}

const handleActionClick = (action: QuickAction) => {
  action.action()
  emit('actionClick', action)
}

// 监听路由变化，自动展开当前活跃的菜单项
// 注意：由于现在使用平级路由结构，不再需要子菜单展开逻辑
watch(route, (newRoute) => {
  // 平级路由结构下，不需要特殊的展开逻辑
}, { immediate: true })

// 监听折叠状态变化
watch(() => props.collapsed, (collapsed) => {
  if (collapsed) {
    expandedItems.value = []
  }
})
</script>

<style scoped>
.navigation-menu {
  padding: 16px 0;
  height: 100%;
  overflow-y: auto;
}

.nav-section {
  margin-bottom: 24px;
}

.nav-section:last-child {
  margin-bottom: 0;
}

.nav-section-title {
  font-size: 12px;
  font-weight: 600;
  color: var(--text-tertiary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 8px;
  padding: 0 16px;
}

.nav-menu {
  list-style: none;
  padding: 0;
  margin: 0;
}

.nav-item {
  margin-bottom: 2px;
  position: relative;
}

.nav-link,
.nav-action {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  border-radius: 10px;
  text-decoration: none;
  color: var(--text-secondary);
  transition: all var(--transition-fast);
  position: relative;
  overflow: visible;
  width: 100%;
  border: none;
  background: none;
  cursor: pointer;
  font-family: inherit;
}

.nav-link:hover,
.nav-action:hover {
  background-color: var(--bg-tertiary);
  color: var(--text-primary);
  transform: translateX(4px);
}

.nav-link.active {
  background: linear-gradient(135deg, var(--color-primary-500) 0%, var(--color-primary-600) 100%);
  color: white;
  box-shadow: var(--shadow-md);
}

.nav-icon-wrapper {
  position: relative;
  min-width: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.nav-icon {
  font-size: 18px;
  transition: all var(--transition-fast);
}

.nav-indicator {
  position: absolute;
  right: -6px;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 4px;
  background-color: white;
  border-radius: 50%;
  opacity: 0.9;
}

.nav-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  min-width: 0;
}

.nav-text {
  font-size: 14px;
  font-weight: 500;
  line-height: 1.2;
}

.nav-description {
  font-size: 12px;
  color: var(--text-tertiary);
  line-height: 1.2;
  margin-top: 2px;
}

.nav-arrow {
  font-size: 14px;
  transition: transform var(--transition-fast);
}

.nav-arrow.expanded {
  transform: rotate(90deg);
}

.nav-shortcut {
  font-size: 11px;
  color: var(--text-tertiary);
  background: var(--bg-tertiary);
  padding: 2px 6px;
  border-radius: 4px;
  font-family: monospace;
}

/* 子菜单样式 */
.nav-submenu {
  list-style: none;
  padding: 0;
  margin: 4px 0 0 0;
  padding-left: 32px;
  border-left: 2px solid var(--border-light);
  margin-left: 16px;
}

.nav-subitem {
  margin-bottom: 2px;
}

.nav-sublink {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border-radius: 6px;
  text-decoration: none;
  color: var(--text-secondary);
  transition: all var(--transition-fast);
  font-size: 13px;
}

.nav-sublink:hover {
  background-color: var(--bg-tertiary);
  color: var(--text-primary);
}

.nav-sublink.active {
  background-color: var(--color-primary-100);
  color: var(--color-primary-700);
}

.nav-subicon {
  font-size: 14px;
}

.nav-subtext {
  font-weight: 500;
}

/* 工具提示样式 */
.nav-tooltip {
  position: absolute;
  left: calc(100% + 8px);
  top: 50%;
  transform: translateY(-50%);
  background: var(--bg-elevated);
  border: 1px solid var(--border-base);
  border-radius: 8px;
  padding: 12px;
  box-shadow: var(--shadow-lg);
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all var(--transition-fast);
  min-width: 200px;
  max-width: 300px;
}

.nav-link:hover .nav-tooltip,
.nav-action:hover .nav-tooltip {
  opacity: 1;
  visibility: visible;
}

.tooltip-content {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.tooltip-title {
  font-size: 14px;
  font-weight: 600;
  color: var(--text-primary);
}

.tooltip-description {
  font-size: 12px;
  color: var(--text-secondary);
}

.tooltip-children {
  margin-top: 8px;
  padding-top: 8px;
  border-top: 1px solid var(--border-light);
}

.tooltip-child {
  font-size: 12px;
  color: var(--text-secondary);
  padding: 2px 0;
}

.tooltip-arrow {
  position: absolute;
  left: -6px;
  top: 50%;
  transform: translateY(-50%);
  width: 0;
  height: 0;
  border-top: 6px solid transparent;
  border-bottom: 6px solid transparent;
  border-right: 6px solid var(--bg-elevated);
}

/* 子菜单动画 */
.submenu-enter-active,
.submenu-leave-active {
  transition: all var(--transition-normal);
  overflow: hidden;
}

.submenu-enter-from,
.submenu-leave-to {
  max-height: 0;
  opacity: 0;
  transform: translateY(-10px);
}

.submenu-enter-to,
.submenu-leave-from {
  max-height: 500px;
  opacity: 1;
  transform: translateY(0);
}

/* 滚动条样式 */
.navigation-menu::-webkit-scrollbar {
  width: 4px;
}

.navigation-menu::-webkit-scrollbar-track {
  background: transparent;
}

.navigation-menu::-webkit-scrollbar-thumb {
  background: var(--border-dark);
  border-radius: 2px;
}

.navigation-menu::-webkit-scrollbar-thumb:hover {
  background: var(--border-darker);
}
</style>
