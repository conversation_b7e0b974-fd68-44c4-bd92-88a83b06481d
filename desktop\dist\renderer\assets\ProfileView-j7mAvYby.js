import{d as c,o as v,a as u,b as s,f as o,w as l,u as a,ai as p,h as _,aj as f,ak as k,v as m,x as w,_ as C}from"./index-BvDCFwwL.js";const g={class:"profile-view"},x={class:"profile-content"},V={class:"quick-nav"},B={class:"nav-icon"},$={class:"nav-icon"},b={class:"nav-icon"},N=c({__name:"ProfileView",setup(P){const r=m(),e=i=>{r.push(i)};return v(()=>{console.log("个人中心页面已加载")}),(i,n)=>{const t=_("el-icon");return w(),u("div",g,[n[6]||(n[6]=s("div",{class:"page-header"},[s("h1",{class:"page-title"},"个人中心"),s("p",{class:"page-description"},"用户信息和统计")],-1)),s("div",x,[s("div",V,[s("div",{class:"nav-card",onClick:n[0]||(n[0]=d=>e("/profile/reports"))},[s("div",B,[o(t,null,{default:l(()=>[o(a(p))]),_:1})]),n[3]||(n[3]=s("div",{class:"nav-content"},[s("h3",null,"学习报告"),s("p",null,"查看学习统计")],-1))]),s("div",{class:"nav-card",onClick:n[1]||(n[1]=d=>e("/profile/account"))},[s("div",$,[o(t,null,{default:l(()=>[o(a(f))]),_:1})]),n[4]||(n[4]=s("div",{class:"nav-content"},[s("h3",null,"账号设置"),s("p",null,"管理账号信息")],-1))]),s("div",{class:"nav-card",onClick:n[2]||(n[2]=d=>e("/profile/network"))},[s("div",b,[o(t,null,{default:l(()=>[o(a(k))]),_:1})]),n[5]||(n[5]=s("div",{class:"nav-content"},[s("h3",null,"网络设置"),s("p",null,"配置网络连接")],-1))])])])])}}}),j=C(N,[["__scopeId","data-v-1947eb2a"]]);export{j as default};
