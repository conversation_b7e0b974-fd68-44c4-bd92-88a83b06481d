<!--
  嵌入式TXT阅读器组件
  集成到主框架中的连续滚动阅读器，支持完整文档加载和滚动阅读
  新增左侧面板布局和目录导航功能
-->
<template>
  <div class="embedded-txt-reader">
    <!-- 阅读器工具栏 -->
    <div class="reader-toolbar">
      <div class="toolbar-left">
        <el-button
          type="primary"
          :icon="ArrowLeft"
          @click="handleGoBack"
          size="default"
        >
          返回图书列表
        </el-button>

        <div class="book-info" v-if="currentBook">
          <span class="book-title">{{ currentBook.title }}</span>
          <span class="reading-progress">{{ progressText }}</span>
        </div>
      </div>

      <div class="toolbar-center">
        <!-- AI学习功能按钮组 -->
        <div class="ai-buttons-group">
          <el-button
            :icon="User"
            @click="toggleAiGuidePanel"
            size="small"
            title="AI学习导引 - 让专家在您身旁导引您，让您学习更加轻松"
            :type="showAiGuidePanel ? 'primary' : 'default'"
          >
            AI学习导引
          </el-button>

        

          <el-button
            :icon="ChatDotRound"
            @click="toggleAiChatPanel"
            size="small"
            title="AI学习对话 - 让每一次问答都成为思维的跳板，跃向更深邃的理解"
            :type="showAiChatPanel ? 'primary' : 'default'"
          >
            AI学习对话
          </el-button>

          <el-button
            :icon="Compass"
            @click="toggleAiExplorePanel"
            size="small"
            title="AI学习探究 - 让每一次追问都点亮新的知识星空"
            :type="showAiExplorePanel ? 'primary' : 'default'"
          >
            AI学习探究
          </el-button>

          <el-button
            :icon="TrendCharts"
            @click="toggleAiEvaluationPanel"
            size="small"
            title="AI评测 - 智能评估学习效果，提供个性化学习建议"
            :type="showAiEvaluationPanel ? 'primary' : 'default'"
          >
            AI评测
          </el-button>

          <el-button
            :icon="EditPen"
            @click="toggleEditorPanel"
            size="small"
            title="编辑器 - 提供文本编辑功能"
            :type="showEditorPanel ? 'primary' : 'default'"
          >
            笔记编辑器
          </el-button>
        </div>
      </div>

      <div class="toolbar-right">
        <el-button
          :icon="Search"
          @click="toggleSearchPanel"
          size="small"
          title="搜索文本"
          :type="showSearchPanel ? 'primary' : 'default'"
        />
        <el-button
          :icon="Menu"
          @click="toggleTocPanel"
          size="small"
          title="目录导航"
          :type="showTocPanel ? 'primary' : 'default'"
        />
        <el-button
          :icon="Setting"
          @click="toggleSettingsPanel"
          size="small"
          title="阅读设置"
          :type="showSettings ? 'primary' : 'default'"
        />
      </div>
    </div>



    <!-- 主内容区域 -->
    <div class="reader-body">
      <!-- 左侧面板 -->
      <div class="left-panel" :class="{ 'panel-visible': showLeftPanel }">
        <!-- 搜索面板 -->
        <div v-if="showSearchPanel" class="search-panel full-height">
          <div class="panel-header">
            <h3>搜索文本</h3>
            <el-button link @click="closeSearchPanel" :icon="Close" size="small" />
          </div>

          <div class="search-content">
            <!-- 搜索输入框 -->
            <div class="search-input-wrapper">
              <el-input
                ref="searchInputRef"
                v-model="searchQuery"
                placeholder="搜索文本..."
                @keyup.enter="handleSearch"
                @input="handleSearchInput"
                clearable
                size="small"
              >
                <template #append>
                  <el-button @click="handleSearch" :icon="Search" />
                </template>
              </el-input>
            </div>

            <!-- 搜索结果导航 -->
            <div v-if="searchResults.length > 0" class="search-results-header">
              <span>找到 {{ searchResults.length }} 个结果</span>
              <div class="search-navigation">
                <el-button
                  size="small"
                  :disabled="currentSearchIndex <= 0"
                  @click="goToPreviousResult"
                  :icon="ArrowUp"
                />
                <span class="search-index">{{ currentSearchIndex + 1 }} / {{ searchResults.length }}</span>
                <el-button
                  size="small"
                  :disabled="currentSearchIndex >= searchResults.length - 1"
                  @click="goToNextResult"
                  :icon="ArrowDown"
                />
                <el-button
                  @click="clearSearch"
                  size="small"
                  :icon="Close"
                  title="清除搜索"
                />
              </div>
            </div>

            <!-- 搜索结果列表 -->
            <div class="search-results-list" v-if="searchResults.length > 0">
              <div
                v-for="(result, index) in searchResults.slice(0, 20)"
                :key="index"
                class="search-result-item"
                :class="{ active: index === currentSearchIndex }"
                @click="jumpToSearchResult(index)"
              >
                <span class="result-index">{{ index + 1 }}</span>
                <span class="result-text">{{ getResultPreview(result) }}</span>
              </div>
              <div v-if="searchResults.length > 20" class="more-results">
                还有 {{ searchResults.length - 20 }} 个结果...
              </div>
            </div>

            <!-- 无搜索结果时的提示 -->
            <div v-else-if="searchQuery && searchResults.length === 0" class="no-results">
              <el-icon><Search /></el-icon>
              <span>未找到匹配的内容</span>
            </div>

            <!-- 搜索提示 -->
            <div v-else class="search-tips">
              <el-icon><Search /></el-icon>
              <span>输入关键词开始搜索</span>
            </div>
          </div>
        </div>

        <!-- 目录导航面板 -->
        <div v-if="showTocPanel" class="toc-panel full-height">
          <div class="panel-header">
            <h3>目录</h3>
            <el-button link @click="closeTocPanel" :icon="Close" size="small" />
          </div>

          <div class="toc-content">
            <div v-if="isLoadingToc" class="toc-loading">
              <el-icon class="loading-icon"><Loading /></el-icon>
              <span>正在生成目录...</span>
            </div>

            <div v-else-if="tableOfContents.length === 0" class="toc-empty">
              <el-icon><Document /></el-icon>
              <span>未找到目录结构</span>
            </div>

            <div v-else class="toc-tree">
              <div
                v-for="item in tableOfContents"
                :key="item.id"
                class="toc-item"
                :class="{
                  'toc-level-1': item.level === 1,
                  'toc-level-2': item.level === 2,
                  'toc-level-3': item.level === 3,
                  'active': currentChapter === item.id
                }"
                @click="jumpToChapter(item)"
              >
                <span class="toc-title">{{ item.title }}</span>
                <span class="toc-progress" v-if="item.progress !== undefined">{{ Math.round(item.progress) }}%</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 阅读设置面板 -->
        <div v-if="showSettings" class="settings-panel full-height">
          <div class="panel-header">
            <h3>阅读设置</h3>
            <el-button link @click="closeSettingsPanel" :icon="Close" size="small" />
          </div>

          <div class="settings-content">
            <div class="setting-group">
              <label>字体大小</label>
              <el-slider
                v-model="settings.font.size"
                :min="12"
                :max="32"
                :step="1"
                show-input
                @change="updateSettings"
              />
            </div>

            <div class="setting-group">
              <label>行间距</label>
              <el-slider
                v-model="settings.font.lineHeight"
                :min="1.0"
                :max="3.0"
                :step="0.1"
                show-input
                @change="updateSettings"
              />
            </div>

            <div class="setting-group">
              <label>字体族</label>
              <el-select v-model="settings.font.family" @change="updateSettings" style="width: 100%">
                <el-option label="微软雅黑" value="Microsoft YaHei" />
                <el-option label="宋体" value="SimSun" />
                <el-option label="黑体" value="SimHei" />
                <el-option label="楷体" value="KaiTi" />
              </el-select>
            </div>

            <div class="setting-group">
              <label>主题模式</label>
              <p class="setting-note">主题设置已移至全局设置，请使用侧边栏或菜单栏的主题切换功能</p>
              <p class="setting-note">当前主题: {{ currentGlobalTheme }}</p>
            </div>
          </div>
        </div>

        <!-- AI学习导引面板 -->
        <div v-if="showAiGuidePanel" class="ai-guide-panel full-height">
          <div class="panel-header">
            <h3>AI学习导引</h3>
            <el-button link @click="closeAiGuidePanel" :icon="Close" size="small" />
          </div>

          <div class="ai-panel-content">
            <div class="ai-panel-description">
              <el-icon><User /></el-icon>
              <p>让专家在您身旁导引您，让您学习更加轻松</p>
            </div>

            <div class="ai-panel-placeholder">
              <div class="placeholder-content">
                <h4>AI学习导引功能</h4>
                <p>这里将显示AI学习导引的内容，包括：</p>
                <ul>
                  <li>智能学习路径推荐</li>
                  <li>个性化学习建议</li>
                  <li>学习进度跟踪</li>
                  <li>专家级指导意见</li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        <!-- 编辑器面板 -->
        <div v-if="showEditorPanel" class="editor-panel full-height">
          <div class="panel-header">
            <h3>编辑器</h3>
            <el-button link @click="closeEditorPanel" :icon="Close" size="small" />
          </div>

          <div class="ai-panel-content">
            <div class="ai-panel-description">
              <el-icon><EditPen /></el-icon>
              <p>提供文本编辑功能</p>
            </div>

            <div class="ai-panel-placeholder">
              <div class="placeholder-content">
                <h4>编辑器功能</h4>
                <p>这里将显示编辑器的内容，包括：</p>
                <ul>
                  <li>文本编辑和格式化</li>
                  <li>笔记记录和整理</li>
                  <li>内容标注和高亮</li>
                  <li>文档导出和分享</li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        <!-- AI学习对话面板 -->
        <div v-if="showAiChatPanel" class="ai-chat-panel full-height">
          <div class="panel-header">
            <h3>AI学习对话</h3>
            <el-button link @click="closeAiChatPanel" :icon="Close" size="small" />
          </div>

          <div class="ai-panel-content">
            <div class="ai-panel-description">
              <el-icon><ChatDotRound /></el-icon>
              <p>让每一次问答都成为思维的跳板，跃向更深邃的理解</p>
            </div>

            <div class="ai-panel-placeholder">
              <div class="placeholder-content">
                <h4>AI学习对话功能</h4>
                <p>这里将显示AI对话的内容，包括：</p>
                <ul>
                  <li>智能问答交互</li>
                  <li>学习疑问解答</li>
                  <li>知识点深度探讨</li>
                  <li>个性化学习建议</li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        <!-- AI学习探究面板 -->
        <div v-if="showAiExplorePanel" class="ai-explore-panel full-height">
          <div class="panel-header">
            <h3>AI学习探究</h3>
            <el-button link @click="closeAiExplorePanel" :icon="Close" size="small" />
          </div>

          <div class="ai-panel-content">
            <div class="ai-panel-description">
              <el-icon><Compass /></el-icon>
              <p>让每一次追问都点亮新的知识星空</p>
            </div>

            <div class="ai-panel-placeholder">
              <div class="placeholder-content">
                <h4>AI学习探究功能</h4>
                <p>这里将显示AI探究的内容，包括：</p>
                <ul>
                  <li>深度知识挖掘</li>
                  <li>关联概念探索</li>
                  <li>学习路径拓展</li>
                  <li>创新思维启发</li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        <!-- AI评测面板 -->
        <div v-if="showAiEvaluationPanel" class="ai-evaluation-panel full-height">
          <div class="panel-header">
            <h3>AI评测</h3>
            <el-button link @click="closeAiEvaluationPanel" :icon="Close" size="small" />
          </div>

          <div class="ai-panel-content">
            <div class="ai-panel-description">
              <el-icon><TrendCharts /></el-icon>
              <p>智能评估学习效果，提供个性化学习建议</p>
            </div>

            <div class="ai-panel-placeholder">
              <div class="placeholder-content">
                <h4>AI评测功能</h4>
                <p>这里将显示AI评测的内容，包括：</p>
                <ul>
                  <li>学习效果智能评估</li>
                  <li>知识掌握程度分析</li>
                  <li>个性化学习建议</li>
                  <li>学习进度跟踪报告</li>
                  <li>薄弱环节识别</li>
                  <li>学习路径优化建议</li>
                </ul>

                <div class="evaluation-preview">
                  <h5>评测维度</h5>
                  <div class="evaluation-metrics">
                    <div class="metric-item">
                      <span class="metric-label">理解深度</span>
                      <div class="metric-bar">
                        <div class="metric-progress" style="width: 75%"></div>
                      </div>
                      <span class="metric-value">75%</span>
                    </div>
                    <div class="metric-item">
                      <span class="metric-label">知识关联</span>
                      <div class="metric-bar">
                        <div class="metric-progress" style="width: 60%"></div>
                      </div>
                      <span class="metric-value">60%</span>
                    </div>
                    <div class="metric-item">
                      <span class="metric-label">应用能力</span>
                      <div class="metric-bar">
                        <div class="metric-progress" style="width: 80%"></div>
                      </div>
                      <span class="metric-value">80%</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 主阅读区域 -->
      <div class="reader-main" :class="{ 'with-left-panel': showLeftPanel }">
        <div
          class="reader-content"
          :style="contentStyle"
          ref="contentRef"
          @scroll="handleScroll"
          tabindex="0"
        >
          <div
            class="text-content"
            v-html="formattedContent"
            @mouseup="handleTextSelection"
          ></div>
        </div>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="isLoading" class="loading-overlay">
      <el-icon class="loading-icon" :size="40">
        <Loading />
      </el-icon>
      <div class="loading-text">正在加载文档...</div>
    </div>

    <!-- 错误状态 -->
    <div v-if="error" class="error-overlay">
      <el-icon class="error-icon"><WarningFilled /></el-icon>
      <div class="error-text">{{ error }}</div>
      <el-button type="primary" @click="retry">重试</el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { useReaderStore } from '@renderer/store/reader'
import { useUnifiedLibraryStore } from '@renderer/store/unifiedLibrary'
import { useThemeStore } from '@renderer/store/theme'
import {
  ArrowLeft,
  Search,
  Setting,
  Close,
  Loading,
  WarningFilled,
  ArrowUp,
  ArrowDown,
  Menu,
  Document,
  // AI功能按钮图标
  User,           // AI学习导引
  EditPen,        // 编辑器
  ChatDotRound,   // AI学习对话
  Compass,        // AI学习探究
  TrendCharts     // AI评测
} from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

// 目录项接口
interface TocItem {
  id: string
  title: string
  level: number
  position: number // 在文档中的字符位置
  lineNumber?: number // 行号（可选）
  progress?: number // 阅读进度百分比
}

// 目录文件结构
interface TocData {
  bookId: string
  bookTitle: string
  generatedAt: number
  items: TocItem[]
}

// Props
interface Props {
  bookId?: string
}

const props = withDefaults(defineProps<Props>(), {
  bookId: ''
})

// Router
const router = useRouter()

// Store
const readerStore = useReaderStore()
const libraryStore = useUnifiedLibraryStore()

// 响应式数据
const contentRef = ref<HTMLElement>()
const searchInputRef = ref<HTMLElement>()
const showSettings = ref(false)
const showSearchPanel = ref(false)
const showTocPanel = ref(false)

// AI学习功能面板状态
const showAiGuidePanel = ref(false)    // AI学习导引面板
const showEditorPanel = ref(false)     // 编辑器面板
const showAiChatPanel = ref(false)     // AI学习对话面板
const showAiExplorePanel = ref(false)  // AI学习探究面板
const showAiEvaluationPanel = ref(false) // AI评测面板
const searchQuery = ref('')
const searchResults = ref<Array<{ index: number; text: string; position: number }>>([])
const currentSearchIndex = ref(-1)
const fullContent = ref('')
const scrollPosition = ref(0)
const contentHeight = ref(0)

// 目录相关数据
const tableOfContents = ref<TocItem[]>([])
const isLoadingToc = ref(false)
const currentChapter = ref<string>('')
const tocFilePath = ref<string>('')

// 阅读设置
const settings = ref({
  font: {
    family: 'Microsoft YaHei',
    size: 16,
    lineHeight: 1.6
  }
})

// 移除护眼颜色选项，使用全局主题系统

// Store实例
const themeStore = useThemeStore()

// 计算属性
const {
  currentBook,
  isLoading,
  error
} = readerStore

// 全局主题信息
const currentGlobalTheme = computed(() => {
  const theme = themeStore.currentTheme
  return theme ? theme.name : '未知'
})

// 移除内部主题系统，使用全局主题

const showLeftPanel = computed(() =>
  showSettings.value ||
  showTocPanel.value ||
  showSearchPanel.value ||
  showAiGuidePanel.value ||
  showEditorPanel.value ||
  showAiChatPanel.value ||
  showAiExplorePanel.value ||
  showAiEvaluationPanel.value
)

const contentStyle = computed(() => ({
  fontFamily: settings.value.font.family,
  fontSize: `${settings.value.font.size}px`,
  lineHeight: settings.value.font.lineHeight,
  height: '100%',
  overflowY: 'auto' as const,
  padding: '20px',
  whiteSpace: 'pre-wrap' as const
}))

const progressText = computed(() => {
  if (contentHeight.value === 0) return '0%'
  const progress = Math.round((scrollPosition.value / contentHeight.value) * 100)
  return `${Math.min(progress, 100)}%`
})

const formattedContent = computed(() => {
  if (!fullContent.value) return ''
  
  let content = fullContent.value
  
  // 如果有搜索结果，高亮显示
  if (searchResults.value.length > 0 && searchQuery.value) {
    const regex = new RegExp(`(${escapeRegExp(searchQuery.value)})`, 'gi')
    content = content.replace(regex, '<mark class="search-highlight">$1</mark>')
  }
  
  return content
})

// 方法
const handleGoBack = () => {
  // 保存阅读进度
  saveReadingProgress()
  // 返回图书列表
  router.push('/library')
}

const handleSearch = () => {
  if (!searchQuery.value.trim()) {
    searchResults.value = []
    currentSearchIndex.value = -1
    return
  }
  
  performSearch(searchQuery.value)
}

const handleSearchInput = () => {
  if (!searchQuery.value.trim()) {
    searchResults.value = []
    currentSearchIndex.value = -1
  }
}

const performSearch = (query: string) => {
  const content = fullContent.value
  const regex = new RegExp(escapeRegExp(query), 'gi')
  const results: Array<{ index: number; text: string; position: number }> = []
  let match
  
  while ((match = regex.exec(content)) !== null) {
    results.push({
      index: results.length,
      text: match[0],
      position: match.index
    })
  }
  
  searchResults.value = results
  currentSearchIndex.value = results.length > 0 ? 0 : -1
  
  if (results.length > 0) {
    scrollToSearchResult(0)
  }
}

const goToPreviousResult = () => {
  if (currentSearchIndex.value > 0) {
    currentSearchIndex.value--
    scrollToSearchResult(currentSearchIndex.value)
  }
}

const goToNextResult = () => {
  if (currentSearchIndex.value < searchResults.value.length - 1) {
    currentSearchIndex.value++
    scrollToSearchResult(currentSearchIndex.value)
  }
}

// 清除搜索
const clearSearch = () => {
  searchQuery.value = ''
  searchResults.value = []
  currentSearchIndex.value = -1
}

// 跳转到指定的搜索结果
const jumpToSearchResult = (index: number) => {
  currentSearchIndex.value = index
  scrollToSearchResult(index)
}

// 获取搜索结果的预览文本
const getResultPreview = (result: { index: number; text: string; position: number }) => {
  const content = fullContent.value
  const start = Math.max(0, result.position - 20)
  const end = Math.min(content.length, result.position + result.text.length + 20)
  const preview = content.substring(start, end)

  // 高亮搜索关键词
  const regex = new RegExp(`(${escapeRegExp(searchQuery.value)})`, 'gi')
  return preview.replace(regex, '【$1】')
}

const scrollToSearchResult = (index: number) => {
  if (!contentRef.value || index < 0 || index >= searchResults.value.length) {
    return
  }

  const result = searchResults.value[index]
  const content = fullContent.value

  // 计算搜索结果在文本中的位置百分比
  const position = result.position
  const totalLength = content.length
  const positionPercent = position / totalLength

  // 计算需要滚动的距离
  const contentElement = contentRef.value
  const maxScrollTop = contentElement.scrollHeight - contentElement.clientHeight
  const targetScrollTop = maxScrollTop * positionPercent

  // 平滑滚动到目标位置
  contentElement.scrollTo({
    top: targetScrollTop,
    behavior: 'smooth'
  })

  // 更新滚动位置状态
  scrollPosition.value = targetScrollTop

  console.log(`跳转到第 ${index + 1} 个搜索结果，位置: ${position}/${totalLength} (${(positionPercent * 100).toFixed(1)}%)`)
}

const handleScroll = (event: Event) => {
  const target = event.target as HTMLElement
  scrollPosition.value = target.scrollTop
  contentHeight.value = target.scrollHeight - target.clientHeight

  // 更新章节进度
  updateChapterProgress()

  // 节流保存进度
  throttledSaveProgress()
}

const handleTextSelection = () => {
  const selection = window.getSelection()
  if (selection && selection.toString().trim()) {
    console.log('Selected text:', selection.toString())
  }
}

const updateSettings = () => {
  // 设置更新后的处理逻辑
  nextTick(() => {
    // 重新计算内容高度
    if (contentRef.value) {
      contentHeight.value = contentRef.value.scrollHeight - contentRef.value.clientHeight
    }
  })
}

// 移除护眼颜色选择功能，使用全局主题系统

const saveReadingProgress = () => {
  if (props.bookId && contentHeight.value > 0) {
    const progress = scrollPosition.value / contentHeight.value
    readerStore.saveProgress(props.bookId, {
      scrollPosition: scrollPosition.value,
      progress: Math.min(progress, 1),
      timestamp: Date.now()
    })
  }
}

// 节流函数
let saveProgressTimer: NodeJS.Timeout | null = null
const throttledSaveProgress = () => {
  if (saveProgressTimer) {
    clearTimeout(saveProgressTimer)
  }
  saveProgressTimer = setTimeout(saveReadingProgress, 1000)
}

const loadBookContent = async () => {
  if (!props.bookId) return

  try {
    // 首先根据bookId获取图书信息
    const bookInfo = await libraryStore.getBook(props.bookId)
    if (!bookInfo) {
      throw new Error('图书不存在')
    }

    // 使用图书的文件路径加载内容
    await readerStore.loadBook(bookInfo.filePath, 'txt')

    // 获取完整文档内容
    if (readerStore.currentReader) {
      fullContent.value = await readerStore.currentReader.getFullContent()

      // 加载目录
      await loadAndGenerateToc(bookInfo.filePath, bookInfo.title)

      // 恢复阅读进度
      const savedProgress = await readerStore.getProgress(props.bookId)
      if (savedProgress && contentRef.value) {
        nextTick(() => {
          if (contentRef.value && savedProgress.scrollPosition) {
            contentRef.value.scrollTop = savedProgress.scrollPosition
          }
        })
      }
    }
  } catch (err) {
    console.error('Failed to load book:', err)
    ElMessage.error('加载图书失败')
  }
}

// 加载和生成目录的统一方法
const loadAndGenerateToc = async (bookFilePath: string, bookTitle: string) => {
  try {
    // 首先尝试加载现有目录
    let tocItems = await loadTableOfContents(bookFilePath)

    // 如果没有现有目录，则生成新目录
    if (tocItems.length === 0 && fullContent.value) {
      tocItems = await generateAndSaveToc(fullContent.value, bookFilePath, bookTitle)
    }

    // 更新目录数据
    tableOfContents.value = tocItems

    if (tocItems.length > 0) {
      console.log(`Loaded ${tocItems.length} TOC items for ${bookTitle}`)
    }
  } catch (error) {
    console.error('Failed to load/generate TOC:', error)
  }
}

const retry = () => {
  readerStore.clearError()
  loadBookContent()
}

const escapeRegExp = (string: string) => {
  return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
}

// 左侧面板控制方法
const toggleSearchPanel = () => {
  if (showSearchPanel.value) {
    // 如果搜索面板已经打开，则关闭
    showSearchPanel.value = false
  } else {
    // 打开搜索面板，同时关闭其他面板
    showSearchPanel.value = true
    showTocPanel.value = false
    showSettings.value = false

    // 聚焦到搜索框
    nextTick(() => {
      if (searchInputRef.value) {
        searchInputRef.value.focus()
      }
    })
  }
}

const toggleTocPanel = () => {
  if (showTocPanel.value) {
    // 如果目录面板已经打开，则关闭
    showTocPanel.value = false
  } else {
    // 打开目录面板，同时关闭其他面板
    showTocPanel.value = true
    showSettings.value = false
    showSearchPanel.value = false
  }
}

const toggleSettingsPanel = () => {
  if (showSettings.value) {
    // 如果设置面板已经打开，则关闭
    showSettings.value = false
  } else {
    // 打开设置面板，同时关闭其他面板
    showSettings.value = true
    showTocPanel.value = false
    showSearchPanel.value = false
  }
}

const closeSearchPanel = () => {
  showSearchPanel.value = false
}

const closeTocPanel = () => {
  showTocPanel.value = false
}

const closeSettingsPanel = () => {
  showSettings.value = false
}

// AI学习功能面板控制方法
const toggleAiGuidePanel = () => {
  if (showAiGuidePanel.value) {
    // 如果AI学习导引面板已经打开，则关闭
    showAiGuidePanel.value = false
  } else {
    // 打开AI学习导引面板，同时关闭其他面板
    showAiGuidePanel.value = true
    showEditorPanel.value = false
    showAiChatPanel.value = false
    showAiExplorePanel.value = false
    showAiEvaluationPanel.value = false
    showSettings.value = false
    showTocPanel.value = false
    showSearchPanel.value = false
  }
}

const toggleEditorPanel = () => {
  if (showEditorPanel.value) {
    // 如果编辑器面板已经打开，则关闭
    showEditorPanel.value = false
  } else {
    // 打开编辑器面板，同时关闭其他面板
    showEditorPanel.value = true
    showAiGuidePanel.value = false
    showAiChatPanel.value = false
    showAiExplorePanel.value = false
    showAiEvaluationPanel.value = false
    showSettings.value = false
    showTocPanel.value = false
    showSearchPanel.value = false
  }
}

const toggleAiChatPanel = () => {
  if (showAiChatPanel.value) {
    // 如果AI学习对话面板已经打开，则关闭
    showAiChatPanel.value = false
  } else {
    // 打开AI学习对话面板，同时关闭其他面板
    showAiChatPanel.value = true
    showAiGuidePanel.value = false
    showEditorPanel.value = false
    showAiExplorePanel.value = false
    showAiEvaluationPanel.value = false
    showSettings.value = false
    showTocPanel.value = false
    showSearchPanel.value = false
  }
}

const toggleAiExplorePanel = () => {
  if (showAiExplorePanel.value) {
    // 如果AI学习探究面板已经打开，则关闭
    showAiExplorePanel.value = false
  } else {
    // 打开AI学习探究面板，同时关闭其他面板
    showAiExplorePanel.value = true
    showAiGuidePanel.value = false
    showEditorPanel.value = false
    showAiChatPanel.value = false
    showAiEvaluationPanel.value = false
    showSettings.value = false
    showTocPanel.value = false
    showSearchPanel.value = false
  }
}

const toggleAiEvaluationPanel = () => {
  if (showAiEvaluationPanel.value) {
    // 如果AI评测面板已经打开，则关闭
    showAiEvaluationPanel.value = false
  } else {
    // 打开AI评测面板，同时关闭其他面板
    showAiEvaluationPanel.value = true
    showAiGuidePanel.value = false
    showEditorPanel.value = false
    showAiChatPanel.value = false
    showAiExplorePanel.value = false
    showSettings.value = false
    showTocPanel.value = false
    showSearchPanel.value = false
  }
}

// AI面板关闭方法
const closeAiGuidePanel = () => {
  showAiGuidePanel.value = false
}

const closeEditorPanel = () => {
  showEditorPanel.value = false
}

const closeAiChatPanel = () => {
  showAiChatPanel.value = false
}

const closeAiExplorePanel = () => {
  showAiExplorePanel.value = false
}

const closeAiEvaluationPanel = () => {
  showAiEvaluationPanel.value = false
}

// 目录解析算法
const parseTableOfContents = (content: string): TocItem[] => {
  const lines = content.split('\n')
  const tocItems: TocItem[] = []
  let position = 0

  // 章节标题匹配模式
  const patterns = [
    // 中文章节模式
    { regex: /^第[一二三四五六七八九十\d]+章\s*(.*)$/i, level: 1 },
    { regex: /^第[一二三四五六七八九十\d]+节\s*(.*)$/i, level: 2 },
    { regex: /^第[一二三四五六七八九十\d]+部分\s*(.*)$/i, level: 1 },
    { regex: /^第[一二三四五六七八九十\d]+篇\s*(.*)$/i, level: 1 },

    // 英文章节模式
    { regex: /^Chapter\s+(\d+)\s*[:\-\s]*(.*)$/i, level: 1 },
    { regex: /^Section\s+(\d+)\s*[:\-\s]*(.*)$/i, level: 2 },
    { regex: /^Part\s+(\d+)\s*[:\-\s]*(.*)$/i, level: 1 },

    // 数字章节模式
    { regex: /^(\d+)\.\s*(.+)$/i, level: 1 },
    { regex: /^(\d+)\.(\d+)\s*(.+)$/i, level: 2 },
    { regex: /^(\d+)\.(\d+)\.(\d+)\s*(.+)$/i, level: 3 },

    // 其他常见模式
    { regex: /^[【\[]第[一二三四五六七八九十\d]+章[】\]]\s*(.*)$/i, level: 1 },
    { regex: /^[【\[]第[一二三四五六七八九十\d]+节[】\]]\s*(.*)$/i, level: 2 },
    { regex: /^[★☆]\s*(.+)$/i, level: 1 },
    { regex: /^[■□]\s*(.+)$/i, level: 2 },
  ]

  lines.forEach((line, lineIndex) => {
    const trimmedLine = line.trim()
    if (!trimmedLine) {
      position += line.length + 1 // +1 for newline
      return
    }

    // 检查是否匹配任何章节模式
    for (const pattern of patterns) {
      const match = trimmedLine.match(pattern.regex)
      if (match) {
        let title = ''

        // 提取标题
        if (pattern.regex.source.includes('(.*)')) {
          title = match[match.length - 1]?.trim() || trimmedLine
        } else {
          title = trimmedLine
        }

        // 如果标题为空，使用整行作为标题
        if (!title) {
          title = trimmedLine
        }

        // 生成唯一ID
        const id = `chapter-${tocItems.length + 1}-${lineIndex}`

        tocItems.push({
          id,
          title,
          level: pattern.level,
          position,
          lineNumber: lineIndex + 1
        })

        break // 找到匹配后跳出循环
      }
    }

    position += line.length + 1 // +1 for newline
  })

  return tocItems
}

// 生成目录文件路径
const generateTocFilePath = (bookFilePath: string): string => {
  // 使用字符串操作替代path模块
  const lastSlashIndex = Math.max(bookFilePath.lastIndexOf('/'), bookFilePath.lastIndexOf('\\'))
  const dir = lastSlashIndex >= 0 ? bookFilePath.substring(0, lastSlashIndex) : ''

  const filename = lastSlashIndex >= 0 ? bookFilePath.substring(lastSlashIndex + 1) : bookFilePath
  const lastDotIndex = filename.lastIndexOf('.')
  const basename = lastDotIndex >= 0 ? filename.substring(0, lastDotIndex) : filename

  return dir ? `${dir}/${basename}_toc.json` : `${basename}_toc.json`
}

// 加载目录文件
const loadTableOfContents = async (bookFilePath: string): Promise<TocItem[]> => {
  try {
    const tocPath = generateTocFilePath(bookFilePath)
    tocFilePath.value = tocPath

    // 尝试读取现有的目录文件
    const tocExists = await window.electronAPI.file.exists(tocPath)

    if (tocExists) {
      const tocBuffer = await window.electronAPI.file.read(tocPath)
      const tocContent = new TextDecoder('utf-8').decode(tocBuffer)
      const tocData: TocData = JSON.parse(tocContent)
      return tocData.items || []
    }

    return []
  } catch (error) {
    console.error('Failed to load table of contents:', error)
    return []
  }
}

// 生成并保存目录
const generateAndSaveToc = async (content: string, bookFilePath: string, bookTitle: string): Promise<TocItem[]> => {
  try {
    isLoadingToc.value = true

    // 解析目录
    const tocItems = parseTableOfContents(content)

    if (tocItems.length > 0) {
      // 创建目录数据
      const tocData: TocData = {
        bookId: props.bookId || '',
        bookTitle,
        generatedAt: Date.now(),
        items: tocItems
      }

      // 保存到文件 - 暂时禁用文件保存功能
      const tocPath = generateTocFilePath(bookFilePath)
      console.log(`Generated TOC for ${bookTitle}, would save to: ${tocPath}`)
      // TODO: 实现文件保存功能
      // const tocContent = JSON.stringify(tocData, null, 2)
      // const tocBuffer = new TextEncoder().encode(tocContent)
      // await window.electronAPI.invoke('file:write', tocPath, tocBuffer)

      console.log(`Generated TOC with ${tocItems.length} items for ${bookTitle}`)
    }

    return tocItems
  } catch (error) {
    console.error('Failed to generate table of contents:', error)
    return []
  } finally {
    isLoadingToc.value = false
  }
}

// 跳转到章节
const jumpToChapter = (item: TocItem) => {
  if (!contentRef.value) return

  try {
    // 根据字符位置计算滚动位置
    const textContent = fullContent.value
    const beforeText = textContent.substring(0, item.position)
    const lines = beforeText.split('\n')

    // 估算滚动位置（基于行高）
    const lineHeight = parseFloat(getComputedStyle(contentRef.value).lineHeight) || 24
    const estimatedScrollTop = (lines.length - 1) * lineHeight

    // 滚动到目标位置
    contentRef.value.scrollTop = estimatedScrollTop

    // 更新当前章节
    currentChapter.value = item.id

    // 高亮显示目标章节（可选）
    highlightChapter(item.title)

  } catch (error) {
    console.error('Failed to jump to chapter:', error)
    ElMessage.error('跳转失败')
  }
}

// 高亮显示章节标题
const highlightChapter = (title: string) => {
  // 临时高亮显示章节标题
  const regex = new RegExp(`(${escapeRegExp(title)})`, 'gi')
  const content = fullContent.value

  // 添加临时高亮样式
  const highlightedContent = content.replace(regex, '<mark class="chapter-highlight">$1</mark>')

  // 更新内容（临时）
  nextTick(() => {
    // 3秒后移除高亮
    setTimeout(() => {
      // 这里可以添加移除高亮的逻辑
    }, 3000)
  })
}

// 更新章节阅读进度
const updateChapterProgress = () => {
  if (tableOfContents.value.length === 0 || contentHeight.value === 0) return

  const currentProgress = scrollPosition.value / contentHeight.value

  // 更新每个章节的阅读进度
  tableOfContents.value.forEach((item, index) => {
    const nextItem = tableOfContents.value[index + 1]
    const itemProgress = nextItem
      ? (item.position / fullContent.value.length)
      : 1

    if (currentProgress >= itemProgress) {
      item.progress = 100
    } else if (currentProgress >= (item.position / fullContent.value.length)) {
      const chapterLength = nextItem
        ? (nextItem.position - item.position)
        : (fullContent.value.length - item.position)

      const chapterProgress = (scrollPosition.value * fullContent.value.length / contentHeight.value - item.position) / chapterLength
      item.progress = Math.max(0, Math.min(100, chapterProgress * 100))
    } else {
      item.progress = 0
    }
  })

  // 更新当前章节
  const currentItem = tableOfContents.value.find(item => {
    const itemScrollPos = (item.position / fullContent.value.length) * contentHeight.value
    return scrollPosition.value >= itemScrollPos - 100 // 100px 容差
  })

  if (currentItem) {
    currentChapter.value = currentItem.id
  }
}

// 生命周期
onMounted(() => {
  loadBookContent()
  
  // 聚焦到内容区域
  nextTick(() => {
    contentRef.value?.focus()
  })
})

onUnmounted(() => {
  // 保存进度并清理
  saveReadingProgress()
  if (saveProgressTimer) {
    clearTimeout(saveProgressTimer)
  }
})

// 监听错误
watch(() => error?.value, (newError) => {
  if (newError) {
    ElMessage.error(newError)
  }
})
</script>

<style scoped>
.embedded-txt-reader {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: var(--el-bg-color);
}

/* 工具栏样式 */
.reader-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid var(--el-border-color);
  background: var(--el-bg-color-page);
  flex-shrink: 0;
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: 16px;
  flex: 1;
}

.toolbar-center {
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 1;
}

.toolbar-right {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
  justify-content: flex-end;
}

.book-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.book-title {
  font-weight: 600;
  color: var(--el-text-color-primary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 200px;
}

.reading-progress {
  font-size: 12px;
  color: var(--el-text-color-secondary);
  white-space: nowrap;
}



/* 主体布局样式 */
.reader-body {
  display: flex;
  flex: 1;
  overflow: hidden;
}

/* 左侧面板样式 */
.left-panel {
  width: 0;
  overflow: hidden;
  background: var(--el-bg-color-page);
  border-right: 1px solid var(--el-border-color);
  transition: width 0.3s ease;
  flex-shrink: 0;
}

.left-panel.panel-visible {
  width: 320px;
}

/* 面板头部样式 */
.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid var(--el-border-color);
  background: var(--el-bg-color);
}

.panel-header h3 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

/* 搜索面板样式 */
.search-panel {
  display: flex;
  flex-direction: column;
  background: var(--el-bg-color-page);
}

.search-panel.full-height {
  height: 100%;
}

.search-content {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.search-input-wrapper {
  margin-bottom: 0;
}

.search-results-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: var(--el-text-color-secondary);
  padding: 8px 0;
  border-bottom: 1px solid var(--el-border-color-lighter);
}

.search-navigation {
  display: flex;
  align-items: center;
  gap: 8px;
}

.search-index {
  min-width: 60px;
  text-align: center;
}

/* 搜索结果列表样式 */
.search-results-list {
  flex: 1;
  overflow-y: auto;
  max-height: 400px;
}

.search-result-item {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  margin-bottom: 4px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid transparent;
}

.search-result-item:hover {
  background-color: var(--el-fill-color-light);
  border-color: var(--el-border-color);
}

.search-result-item.active {
  background-color: var(--el-color-primary-light-9);
  border-color: var(--el-color-primary);
  color: var(--el-color-primary);
}

.result-index {
  font-size: 12px;
  font-weight: bold;
  color: var(--el-text-color-secondary);
  min-width: 24px;
  text-align: center;
  margin-right: 8px;
}

.search-result-item.active .result-index {
  color: var(--el-color-primary);
}

.result-text {
  font-size: 13px;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex: 1;
}

.more-results {
  text-align: center;
  padding: 8px;
  font-size: 12px;
  color: var(--el-text-color-secondary);
  font-style: italic;
}

/* 搜索提示和无结果样式 */
.search-tips,
.no-results {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: var(--el-text-color-secondary);
  font-size: 14px;
}

.search-tips .el-icon,
.no-results .el-icon {
  margin-bottom: 8px;
  font-size: 24px;
}

/* 目录面板样式 */
.toc-panel {
  display: flex;
  flex-direction: column;
}

.toc-panel.full-height {
  height: 100%;
}

.toc-content {
  height: 800px !important;
  overflow-y: auto;
  padding: 8px 0;
}

.toc-loading,
.toc-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: var(--el-text-color-secondary);
  font-size: 14px;
}

.toc-loading .loading-icon,
.toc-empty .el-icon {
  margin-bottom: 8px;
  font-size: 24px;
}

.toc-tree {
  padding: 0 8px;
}

.toc-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  margin: 2px 0;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 13px;
  line-height: 1.4;
}

.toc-item:hover {
  background: var(--el-color-primary-light-9);
  color: var(--el-color-primary);
}

.toc-item.active {
  background: var(--el-color-primary-light-8);
  color: var(--el-color-primary);
  font-weight: 500;
}

.toc-item.toc-level-1 {
  font-weight: 500;
  margin-left: 0;
}

.toc-item.toc-level-2 {
  margin-left: 16px;
  font-size: 12px;
  opacity: 0.9;
}

.toc-item.toc-level-3 {
  margin-left: 32px;
  font-size: 12px;
  opacity: 0.8;
}

.toc-title {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.toc-progress {
  font-size: 11px;
  color: var(--el-text-color-secondary);
  margin-left: 8px;
  flex-shrink: 0;
}



/* 设置面板样式 */
.settings-panel {
  display: flex;
  flex-direction: column;
  background: var(--el-bg-color-page);
}

.settings-panel.full-height {
  height: 100%;
}

.settings-content {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.setting-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.setting-group label {
  font-size: 13px;
  font-weight: 500;
  color: var(--el-text-color-primary);
  margin-bottom: 4px;
}

/* AI面板样式 */
.ai-guide-panel,
.editor-panel,
.ai-chat-panel,
.ai-explore-panel {
  display: flex;
  flex-direction: column;
  background: var(--el-bg-color-page);
}

.ai-guide-panel.full-height,
.editor-panel.full-height,
.ai-chat-panel.full-height,
.ai-explore-panel.full-height,
.ai-evaluation-panel.full-height {
  height: 100%;
}

.ai-panel-content {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.ai-panel-description {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: var(--el-bg-color);
  border-radius: 8px;
  border: 1px solid var(--el-border-color-light);
}

.ai-panel-description .el-icon {
  font-size: 20px;
  color: var(--el-color-primary);
}

.ai-panel-description p {
  margin: 0;
  font-size: 14px;
  color: var(--el-text-color-regular);
  line-height: 1.5;
}

.ai-panel-placeholder {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.placeholder-content {
  text-align: center;
  max-width: 280px;
}

.placeholder-content h4 {
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.placeholder-content p {
  margin: 0 0 16px 0;
  font-size: 14px;
  color: var(--el-text-color-regular);
  line-height: 1.5;
}

.placeholder-content ul {
  margin: 0;
  padding-left: 20px;
  text-align: left;
}

.placeholder-content li {
  margin-bottom: 8px;
  font-size: 13px;
  color: var(--el-text-color-secondary);
  line-height: 1.4;
}

/* AI评测面板特有样式 */
.evaluation-preview {
  margin-top: 20px;
  padding: 16px;
  background: var(--el-fill-color-lighter);
  border-radius: 8px;
  border: 1px solid var(--el-border-color-light);
}

.evaluation-preview h5 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.evaluation-metrics {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.metric-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.metric-label {
  min-width: 70px;
  font-size: 12px;
  color: var(--el-text-color-regular);
  font-weight: 500;
}

.metric-bar {
  flex: 1;
  height: 8px;
  background: var(--el-fill-color);
  border-radius: 4px;
  overflow: hidden;
  position: relative;
}

.metric-progress {
  height: 100%;
  background: linear-gradient(90deg, var(--el-color-primary-light-3), var(--el-color-primary));
  border-radius: 4px;
  transition: width 0.3s ease;
}

.metric-value {
  min-width: 35px;
  font-size: 12px;
  font-weight: 600;
  color: var(--el-color-primary);
  text-align: right;
}

/* AI按钮组样式 */
.ai-buttons-group {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
  justify-content: center;
}

.ai-buttons-group .el-button {
  font-size: 12px;
  padding: 8px 12px;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.ai-buttons-group .el-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 主阅读区域样式 */
.reader-main {
  flex: 1;
  overflow: hidden;
  position: relative;
  transition: margin-left 0.3s ease;
}

.reader-main.with-left-panel {
  /* 当左侧面板显示时，主阅读区域不需要额外的margin，因为使用了flex布局 */
}

.reader-content {
  height: 100%;
  overflow-y: auto;
  scroll-behavior: smooth;
}

.text-content {
  max-width: 1000px;
  margin: 0 auto;
  line-height: inherit;
  word-wrap: break-word;
  word-break: break-word;
}

/* 搜索高亮样式 */
.text-content :deep(.search-highlight) {
  background-color: #ffeb3b;
  color: #333;
  padding: 2px 4px;
  border-radius: 2px;
}

/* 章节高亮样式 */
.text-content :deep(.chapter-highlight) {
  background-color: var(--el-color-primary-light-8);
  color: var(--el-color-primary);
  padding: 4px 8px;
  border-radius: 4px;
  font-weight: 600;
  animation: chapter-flash 3s ease-out;
}

@keyframes chapter-flash {
  0% {
    background-color: var(--el-color-primary-light-6);
  }
  100% {
    background-color: var(--el-color-primary-light-8);
  }
}

/* 加载和错误状态样式 */
.loading-overlay,
.error-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background: rgba(255, 255, 255, 0.9);
  z-index: 1000;
}

.loading-icon,
.error-icon {
  margin-bottom: 16px;
  color: var(--el-color-primary);
}

.loading-text,
.error-text {
  font-size: 14px;
  color: var(--el-text-color-secondary);
  margin-bottom: 16px;
}

/* 使用全局主题系统的CSS变量 */

.reader-content {
  background-color: var(--reader-bg-color);
  color: var(--reader-text-color);
}

/* 护眼颜色选择器样式 */
.color-options {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px;
  margin-top: 8px;
}

.color-option {
  padding: 12px;
  border-radius: 8px;
  border: 2px solid transparent;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: center;
  min-height: 60px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.color-option:hover {
  border-color: var(--el-color-primary);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.color-option.active {
  border-color: var(--el-color-primary);
  box-shadow: 0 0 0 1px var(--el-color-primary);
}

.color-name {
  font-size: 12px;
  font-weight: 500;
  margin-bottom: 4px;
}

.color-preview {
  font-size: 16px;
  font-weight: bold;
  opacity: 0.8;
}

/* 滚动条样式 */
.reader-content::-webkit-scrollbar {
  width: 8px;
}

.reader-content::-webkit-scrollbar-track {
  background: var(--el-bg-color);
}

.reader-content::-webkit-scrollbar-thumb {
  background: var(--el-border-color-darker);
  border-radius: 4px;
}

.reader-content::-webkit-scrollbar-thumb:hover {
  background: var(--el-border-color-dark);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .reader-toolbar {
    padding: 8px 12px;
  }

  .toolbar-left {
    gap: 8px;
  }

  .book-info {
    display: none;
  }

  .text-content {
    max-width: 100%;
    padding: 0 8px;
  }

  .search-panel {
    padding: 12px;
  }

  /* 移动端左侧面板调整 */
  .left-panel.panel-visible {
    width: 280px;
  }

  .toc-item.toc-level-2 {
    margin-left: 12px;
  }

  .toc-item.toc-level-3 {
    margin-left: 24px;
  }

  .settings-content {
    padding: 12px;
    gap: 16px;
  }

  /* AI面板移动端样式 */
  .ai-panel-content {
    padding: 12px;
    gap: 12px;
  }

  .ai-buttons-group {
    gap: 6px;
  }

  .ai-buttons-group .el-button {
    font-size: 11px;
    padding: 6px 10px;
  }

  .placeholder-content {
    max-width: 240px;
  }

  .placeholder-content h4 {
    font-size: 14px;
  }

  .placeholder-content p {
    font-size: 13px;
  }

  .placeholder-content li {
    font-size: 12px;
  }
}

@media (max-width: 480px) {
  /* 超小屏幕时左侧面板占据更多空间 */
  .left-panel.panel-visible {
    width: 100%;
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    z-index: 1000;
    background: var(--el-bg-color-page);
    box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
  }

  .reader-main.with-left-panel {
    margin-left: 0;
  }
}
</style>
