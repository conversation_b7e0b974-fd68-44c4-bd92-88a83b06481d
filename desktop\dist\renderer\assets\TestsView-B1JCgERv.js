import{d as o,o as a,a as n,b as e,f as c,h as p,x as _,_ as d}from"./index-N2UppB4L.js";const i={class:"tests-view"},r={class:"tests-content"},l=o({__name:"TestsView",setup(m){return a(()=>{console.log("测试任务页面已加载")}),(f,s)=>{const t=p("el-empty");return _(),n("div",i,[s[0]||(s[0]=e("div",{class:"page-header"},[e("h1",{class:"page-title"},"测试任务"),e("p",{class:"page-description"},"参与在线测试和评估")],-1)),e("div",r,[c(t,{description:"测试任务功能开发中..."})])])}}}),u=d(l,[["__scopeId","data-v-ed6331ce"]]);export{u as default};
