<!--
  导入图书页面
  支持拖拽导入、文件选择导入、文件夹导入，实时显示导入进度
-->
<template>
  <div class="import-view">
    <div class="import-header">
      <h1 class="page-title">导入图书</h1>
      <p class="page-description">支持 EPUB、PDF、MOBI、TXT、AZW3 等格式</p>

      <!-- 导入选项 -->
      <div class="import-options">
        <el-card>
          <template #header>
            <span>导入设置</span>
          </template>
          <div class="options-content">
            <el-checkbox v-model="importOptions.allowDuplicates">
              允许重复文件
            </el-checkbox>
            <el-checkbox v-model="importOptions.extractCovers">
              提取封面图片
            </el-checkbox>
            <el-checkbox v-model="importOptions.validateFiles">
              验证文件完整性
            </el-checkbox>
          </div>
        </el-card>
      </div>
    </div>

    <div class="import-content">
      <!-- 导入方式选择 -->
      <div class="import-methods">
        <el-tabs v-model="activeTab" @tab-change="handleTabChange">
          <el-tab-pane label="文件导入" name="files">
            <!-- 拖拽上传区域 -->
            <div
              class="upload-area"
              :class="{
                'dragover': isDragOver,
                'disabled': isImporting
              }"
              @drop="handleDrop"
              @dragover="handleDragOver"
              @dragleave="handleDragLeave"
              @click="selectFiles"
            >
              <div class="upload-icon">
                <el-icon><Upload /></el-icon>
              </div>
              <div class="upload-text">
                <h3>拖拽文件到此处或点击选择</h3>
                <p>支持批量导入多个文件</p>
              </div>
              <div class="supported-formats">
                <span
                  v-for="format in supportedFormats"
                  :key="format"
                  class="format-tag"
                >
                  {{ format.toUpperCase() }}
                </span>
              </div>
            </div>
          </el-tab-pane>

          <el-tab-pane label="文件夹导入" name="folder">
            <div class="folder-import">
              <el-button
                type="primary"
                size="large"
                :loading="isImporting"
                @click="selectFolder"
              >
                <el-icon><Folder /></el-icon>
                选择文件夹
              </el-button>
              <p class="folder-tip">
                选择包含电子书文件的文件夹，支持递归扫描子文件夹
              </p>
              <el-checkbox v-model="folderOptions.recursive">
                递归扫描子文件夹
              </el-checkbox>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>

      <!-- 文件选择器 -->
      <input
        ref="fileInput"
        type="file"
        multiple
        :accept="acceptTypes"
        style="display: none"
        @change="handleFileSelect"
      />

      <!-- 文件夹选择器 -->
      <input
        ref="folderInput"
        type="file"
        webkitdirectory
        style="display: none"
        @change="handleFolderSelect"
      />

      <!-- 导入进度 -->
      <div v-if="isImporting || importProgress" class="import-progress">
        <el-card>
          <template #header>
            <div class="progress-header">
              <span>导入进度</span>
              <el-button
                v-if="isImporting"
                size="small"
                type="danger"
                @click="cancelImport"
              >
                取消导入
              </el-button>
            </div>
          </template>

          <div v-if="importProgress" class="progress-content">
            <!-- 总体进度 -->
            <div class="overall-progress">
              <div class="progress-info">
                <span class="current-file">{{ importProgress.current }}</span>
                <span class="progress-text">
                  {{ importProgress.completed }} / {{ importProgress.total }}
                </span>
              </div>
              <el-progress
                :percentage="importProgress.percentage"
                :status="getProgressStatus(importProgress.status)"
                :stroke-width="8"
              />
            </div>

            <!-- 错误列表 -->
            <div v-if="importProgress.errors.length > 0" class="error-list">
              <h4>导入错误</h4>
              <div class="error-items">
                <div
                  v-for="error in importProgress.errors"
                  :key="error.filePath"
                  class="error-item"
                >
                  <el-icon class="error-icon"><Warning /></el-icon>
                  <div class="error-details">
                    <div class="error-file">{{ getFileName(error.filePath) }}</div>
                    <div class="error-message">{{ error.error }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </div>

      <!-- 导入结果 -->
      <div v-if="importResult" class="import-result">
        <el-card>
          <template #header>
            <span>导入结果</span>
          </template>

          <div class="result-summary">
            <div class="summary-item success">
              <el-icon><SuccessFilled /></el-icon>
              <span>成功导入 {{ importResult.successCount }} 本</span>
            </div>
            <div v-if="importResult.errorCount > 0" class="summary-item error">
              <el-icon><CircleCloseFilled /></el-icon>
              <span>导入失败 {{ importResult.errorCount }} 本</span>
            </div>
            <div v-if="importResult.duplicateCount > 0" class="summary-item warning">
              <el-icon><WarningFilled /></el-icon>
              <span>重复文件 {{ importResult.duplicateCount }} 本</span>
            </div>
          </div>

          <div class="result-actions">
            <el-button @click="viewImportedBooks">
              查看导入的图书
            </el-button>
            <el-button @click="clearResult">
              清除结果
            </el-button>
          </div>
        </el-card>
      </div>

      <!-- 最近导入 -->
      <div v-if="recentImports.length > 0" class="recent-imports">
        <el-card>
          <template #header>
            <span>最近导入</span>
          </template>

          <div class="recent-list">
            <div
              v-for="book in recentImports"
              :key="book.id"
              class="recent-item"
              @click="openBook(book)"
            >
              <div class="book-cover">
                <img
                  :src="book.coverPath || '/placeholder-book.png'"
                  :alt="book.title"
                  @error="handleImageError"
                />
              </div>
              <div class="book-info">
                <h4>{{ book.title }}</h4>
                <p>{{ book.author }}</p>
                <div class="book-meta">
                  <span class="format-tag">{{ book.format.toUpperCase() }}</span>
                  <span class="import-time">{{ formatDate(book.addedAt) }}</span>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Upload, Document, Folder, Warning, SuccessFilled,
  CircleCloseFilled, WarningFilled
} from '@element-plus/icons-vue'
import { useBookshelfStore } from '@renderer/store/bookshelf'
import { bookImportService } from '@shared/services/BookImportService'
import type { ImportOptions, ImportProgress, ImportResult } from '@shared/services/BookImportService'
import type { Book } from '@shared/types/database'

const router = useRouter()
const bookshelfStore = useBookshelfStore()

// 从store中解构
const {
  isImporting,
  importProgress,
  startImport,
  updateImportProgress,
  finishImport
} = bookshelfStore

// 本地状态
const isDragOver = ref(false)
const activeTab = ref('files')
const fileInput = ref<HTMLInputElement>()
const folderInput = ref<HTMLInputElement>()
const importResult = ref<ImportResult | null>(null)
const recentImports = ref<Book[]>([])

// 导入选项
const importOptions = ref<ImportOptions>({
  allowDuplicates: false,
  extractCovers: true,
  validateFiles: true,
  onProgress: (progress: ImportProgress) => {
    updateImportProgress(progress)
  }
})

// 文件夹选项
const folderOptions = ref({
  recursive: true
})

// 支持的格式
const supportedFormats = ref(['epub', 'pdf', 'txt', 'mobi', 'azw3'])

// 计算属性
const acceptTypes = computed(() => {
  return supportedFormats.value.map(format => `.${format}`).join(',')
})

// 方法
const handleDragOver = (e: DragEvent) => {
  e.preventDefault()
  if (!isImporting) {
    isDragOver.value = true
  }
}

const handleDragLeave = (e: DragEvent) => {
  e.preventDefault()
  isDragOver.value = false
}

const handleDrop = (e: DragEvent) => {
  e.preventDefault()
  isDragOver.value = false

  if (isImporting) {
    ElMessage.warning('正在导入中，请等待完成')
    return
  }

  const files = Array.from(e.dataTransfer?.files || [])
  processFiles(files)
}

const selectFiles = () => {
  if (isImporting) {
    ElMessage.warning('正在导入中，请等待完成')
    return
  }
  fileInput.value?.click()
}

const selectFolder = () => {
  if (isImporting) {
    ElMessage.warning('正在导入中，请等待完成')
    return
  }
  folderInput.value?.click()
}

const handleFileSelect = (e: Event) => {
  const target = e.target as HTMLInputElement
  const files = Array.from(target.files || [])
  processFiles(files)
  // 清空input值，允许重复选择同一文件
  target.value = ''
}

const handleFolderSelect = (e: Event) => {
  const target = e.target as HTMLInputElement
  const files = Array.from(target.files || [])
  processFiles(files)
  // 清空input值
  target.value = ''
}

const handleTabChange = (tabName: string) => {
  activeTab.value = tabName
}

const processFiles = async (files: File[]) => {
  if (files.length === 0) return

  // 过滤支持的文件格式
  const validFiles = files.filter(file => {
    const ext = '.' + file.name.split('.').pop()?.toLowerCase()
    return supportedFormats.value.includes(ext.substring(1))
  })

  if (validFiles.length === 0) {
    ElMessage.warning('没有找到支持的文件格式')
    return
  }

  if (validFiles.length !== files.length) {
    ElMessage.warning(`已过滤 ${files.length - validFiles.length} 个不支持的文件`)
  }

  try {
    // 开始导入
    startImport()

    // 转换为文件路径（在实际应用中，这里需要处理文件上传到临时目录）
    const filePaths = validFiles.map(file => {
      // TODO: 在实际应用中，需要将File对象保存到临时目录并返回路径
      return file.name // 临时使用文件名
    })

    // 执行导入
    const result = await bookImportService.importFiles(filePaths, importOptions.value)

    // 完成导入
    finishImport(result)
    importResult.value = result

    // 显示结果消息
    if (result.success) {
      ElMessage.success(`成功导入 ${result.successCount} 本图书`)
    } else {
      ElMessage.error(`导入完成，成功 ${result.successCount} 本，失败 ${result.errorCount} 本`)
    }

    // 刷新最近导入列表
    await loadRecentImports()

  } catch (error) {
    console.error('导入失败:', error)
    ElMessage.error('导入失败: ' + (error instanceof Error ? error.message : '未知错误'))
    finishImport({
      success: false,
      imported: [],
      errors: [{
        filePath: 'batch',
        error: error instanceof Error ? error.message : '未知错误',
        timestamp: new Date()
      }],
      duplicates: [],
      totalFiles: validFiles.length,
      successCount: 0,
      errorCount: validFiles.length,
      duplicateCount: 0
    })
  }
}

const cancelImport = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要取消当前导入操作吗？',
      '确认取消',
      {
        confirmButtonText: '确定',
        cancelButtonText: '继续导入',
        type: 'warning'
      }
    )

    // TODO: 实现取消导入逻辑
    ElMessage.info('导入已取消')
  } catch {
    // 用户选择继续导入
  }
}

const getProgressStatus = (status: string) => {
  switch (status) {
    case 'completed':
      return 'success'
    case 'error':
      return 'exception'
    case 'processing':
      return undefined
    default:
      return undefined
  }
}

const getFileName = (filePath: string) => {
  return filePath.split('/').pop() || filePath
}

const viewImportedBooks = () => {
  router.push('/library')
}

const clearResult = () => {
  importResult.value = null
}

const openBook = (book: Book) => {
  router.push(`/reader/${book.id}`)
}

const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement
  img.src = '/placeholder-book.png'
}

const formatFileSize = (bytes: number) => {
  if (!bytes) return '0 B'

  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))

  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
}

const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  const hours = Math.floor(diff / (1000 * 60 * 60))

  if (hours < 1) return '刚刚'
  if (hours < 24) return `${hours}小时前`
  if (hours < 24 * 7) return `${Math.floor(hours / 24)}天前`

  return date.toLocaleDateString()
}

const loadRecentImports = async () => {
  try {
    // TODO: 从数据库加载最近导入的图书
    // const books = await bookRepository.findRecentlyAdded(10)
    // recentImports.value = books
    recentImports.value = []
  } catch (error) {
    console.error('加载最近导入失败:', error)
  }
}

// 监听导入进度变化
watch(() => bookshelfStore.importProgress, (progress) => {
  if (progress && progress.status === 'completed') {
    // 导入完成后的处理
  }
}, { immediate: false })

// 生命周期
onMounted(async () => {
  await loadRecentImports()
})
</script>

<style scoped>
.import-view {
  padding: 24px;
  max-width: 1000px;
  margin: 0 auto;
}

.import-header {
  text-align: center;
  margin-bottom: 32px;
}

.page-title {
  font-size: 28px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin: 0 0 8px 0;
}

.page-description {
  font-size: 16px;
  color: var(--el-text-color-regular);
  margin: 0 0 24px 0;
}

.import-options {
  max-width: 400px;
  margin: 0 auto;
}

.options-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.import-content {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.import-methods {
  margin-bottom: 24px;
}

/* 上传区域 */
.upload-area {
  border: 2px dashed var(--el-border-color);
  border-radius: 16px;
  padding: 48px 24px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  background: var(--el-bg-color-page);
  min-height: 200px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.upload-area:hover,
.upload-area.dragover {
  border-color: var(--el-color-primary);
  background: var(--el-color-primary-light-9);
}

.upload-area.disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

.upload-icon {
  font-size: 48px;
  color: var(--el-color-primary);
  margin-bottom: 16px;
}

.upload-text h3 {
  font-size: 20px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin: 0 0 8px 0;
}

.upload-text p {
  font-size: 14px;
  color: var(--el-text-color-regular);
  margin: 0 0 24px 0;
}

.supported-formats {
  display: flex;
  justify-content: center;
  gap: 8px;
  flex-wrap: wrap;
}

.format-tag {
  background: var(--el-color-primary-light-8);
  color: var(--el-color-primary);
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
}

/* 文件夹导入 */
.folder-import {
  text-align: center;
  padding: 48px 24px;
}

.folder-tip {
  margin: 16px 0;
  color: var(--el-text-color-regular);
}

/* 导入进度 */
.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.progress-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.overall-progress {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.current-file {
  font-weight: 500;
  color: var(--el-text-color-primary);
}

.progress-text {
  color: var(--el-text-color-regular);
  font-size: 14px;
}

/* 错误列表 */
.error-list h4 {
  margin: 0 0 12px 0;
  color: var(--el-color-danger);
}

.error-items {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.error-item {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  padding: 8px;
  background: var(--el-color-danger-light-9);
  border-radius: 6px;
}

.error-icon {
  color: var(--el-color-danger);
  margin-top: 2px;
}

.error-details {
  flex: 1;
}

.error-file {
  font-weight: 500;
  color: var(--el-text-color-primary);
  font-size: 14px;
}

.error-message {
  color: var(--el-color-danger);
  font-size: 12px;
  margin-top: 2px;
}

/* 导入结果 */
.result-summary {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 20px;
}

.summary-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border-radius: 6px;
}

.summary-item.success {
  background: var(--el-color-success-light-9);
  color: var(--el-color-success);
}

.summary-item.error {
  background: var(--el-color-danger-light-9);
  color: var(--el-color-danger);
}

.summary-item.warning {
  background: var(--el-color-warning-light-9);
  color: var(--el-color-warning);
}

.result-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

/* 最近导入 */
.recent-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
  gap: 16px;
}

.recent-item {
  background: var(--el-bg-color-page);
  border: 1px solid var(--el-border-color-light);
  border-radius: 12px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.recent-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.book-cover {
  width: 100%;
  height: 140px;
  background: var(--el-fill-color-light);
  border-radius: 8px;
  margin-bottom: 12px;
  overflow: hidden;
}

.book-cover img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.book-info h4 {
  font-size: 16px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin: 0 0 4px 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.book-info p {
  font-size: 14px;
  color: var(--el-text-color-regular);
  margin: 0 0 8px 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.book-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.import-time {
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .import-view {
    padding: 16px;
  }

  .upload-area {
    padding: 32px 16px;
  }

  .folder-import {
    padding: 32px 16px;
  }

  .recent-list {
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
  }

  .options-content {
    align-items: flex-start;
  }

  .result-actions {
    flex-direction: column;
  }

  .progress-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
}
</style>
