import{d as t,o as a,a as n,b as e,f as c,h as p,x as _,_ as d}from"./index-N2UppB4L.js";const i={class:"notes-view"},r={class:"notes-content"},l=t({__name:"NotesView",setup(m){return a(()=>{console.log("笔记管理页面已加载")}),(f,s)=>{const o=p("el-empty");return _(),n("div",i,[s[0]||(s[0]=e("div",{class:"page-header"},[e("h1",{class:"page-title"},"笔记管理"),e("p",{class:"page-description"},"整理和管理阅读笔记")],-1)),e("div",r,[c(o,{description:"笔记管理功能开发中..."})])])}}}),u=d(l,[["__scopeId","data-v-cb088ab3"]]);export{u as default};
