import{d as I,r as p,a as d,b as s,e as x,f as a,w as o,h as f,u as h,n as V,g as k,t as B,F as N,q as A,C as w,O as E,P as M,M as g,v as S,x as n,_ as T}from"./index-N2UppB4L.js";const D={class:"simple-import-view"},z={class:"import-content"},O={class:"file-selector"},R={class:"selector-content"},U={key:0,class:"import-progress"},q={class:"progress-content"},L={class:"progress-text"},W={key:1,class:"import-results"},X={class:"results-content"},Z={class:"result-text"},j={class:"actions"},G=I({__name:"SimpleImportView",setup(H){const P=S(),_=p(!1),m=p(0),v=p(void 0),c=p(""),i=p([]),F=async()=>{try{const l=await window.electronAPI.file.select([{name:"电子书文件",extensions:["txt","epub","pdf","mobi","azw3"]},{name:"文本文件",extensions:["txt"]},{name:"EPUB文件",extensions:["epub"]},{name:"PDF文件",extensions:["pdf"]},{name:"所有文件",extensions:["*"]}]);l&&l.length>0&&await $(l)}catch(l){console.error("选择文件失败:",l),g.error("选择文件失败")}},$=async l=>{_.value=!0,m.value=0,v.value=void 0,i.value=[],c.value="开始导入...";try{for(let e=0;e<l.length;e++){const r=l[e],t=r.split(/[/\\]/).pop()||r;c.value=`正在导入: ${t}`,m.value=Math.round(e/l.length*100);try{const u=await window.electronAPI.book.add(r);i.value.push({file:t,success:!0,message:`${t} 导入成功`}),g.success(`${t} 导入成功`)}catch(u){console.error(`导入 ${t} 失败:`,u),i.value.push({file:t,success:!1,message:`${t} 导入失败: ${u.message||"未知错误"}`}),g.error(`${t} 导入失败`)}}m.value=100,v.value="success",c.value="导入完成"}catch(e){console.error("导入过程出错:",e),v.value="exception",c.value="导入失败",g.error("导入过程出错")}finally{_.value=!1}},C=()=>{P.push("/library")};return(l,e)=>{const r=f("el-button"),t=f("el-card"),u=f("el-progress"),b=f("el-icon");return n(),d("div",D,[e[7]||(e[7]=s("div",{class:"import-header"},[s("h1",{class:"page-title"},"导入图书"),s("p",{class:"page-description"},"选择电子书文件进行导入")],-1)),s("div",z,[s("div",O,[a(t,null,{header:o(()=>e[0]||(e[0]=[s("span",null,"选择图书文件",-1)])),default:o(()=>[s("div",R,[a(r,{type:"primary",icon:h(V),onClick:F,loading:_.value,size:"large"},{default:o(()=>e[1]||(e[1]=[k(" 选择文件 ")])),_:1,__:[1]},8,["icon","loading"]),e[2]||(e[2]=s("p",{class:"supported-formats"}," 支持格式：TXT, EPUB, PDF, MOBI, AZW3 ",-1))])]),_:1})]),_.value?(n(),d("div",U,[a(t,null,{header:o(()=>e[3]||(e[3]=[s("span",null,"导入进度",-1)])),default:o(()=>[s("div",q,[a(u,{percentage:m.value,status:v.value},null,8,["percentage","status"]),s("p",L,B(c.value),1)])]),_:1})])):x("",!0),i.value.length>0?(n(),d("div",W,[a(t,null,{header:o(()=>e[4]||(e[4]=[s("span",null,"导入结果",-1)])),default:o(()=>[s("div",X,[(n(!0),d(N,null,A(i.value,y=>(n(),d("div",{key:y.file,class:"result-item"},[y.success?(n(),w(b,{key:0,class:"success-icon",color:"#67C23A"},{default:o(()=>[a(h(E))]),_:1})):(n(),w(b,{key:1,class:"error-icon",color:"#F56C6C"},{default:o(()=>[a(h(M))]),_:1})),s("span",Z,B(y.message),1)]))),128))])]),_:1})])):x("",!0),s("div",j,[a(r,{onClick:C},{default:o(()=>e[5]||(e[5]=[k("返回图书列表")])),_:1,__:[5]}),i.value.length>0?(n(),w(r,{key:0,type:"primary",onClick:C},{default:o(()=>e[6]||(e[6]=[k(" 查看导入的图书 ")])),_:1,__:[6]})):x("",!0)])])])}}}),K=T(G,[["__scopeId","data-v-f3c1512f"]]);export{K as default};
