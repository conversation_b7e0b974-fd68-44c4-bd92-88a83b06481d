import{d as o,o as a,a as n,b as e,f as i,h as c,x as l,_ as p}from"./index-BvDCFwwL.js";const _={class:"highlights-view"},d={class:"highlights-content"},r=o({__name:"HighlightsView",setup(h){return a(()=>{console.log("高亮管理页面已加载")}),(g,s)=>{const t=c("el-empty");return l(),n("div",_,[s[0]||(s[0]=e("div",{class:"page-header"},[e("h1",{class:"page-title"},"高亮管理"),e("p",{class:"page-description"},"管理文本高亮标记")],-1)),e("div",d,[i(t,{description:"高亮管理功能开发中..."})])])}}}),f=p(r,[["__scopeId","data-v-027369f1"]]);export{f as default};
