import{d as D,r as T,c as p,o as $,a as d,b as t,e as g,f as e,w as a,g as h,u as l,p as P,h as m,s as E,i as I,t as c,j,k as O,l as w,m as z,n as G,F as L,q as M,v as H,x as r,_ as J}from"./index-BvDCFwwL.js";import{u as K}from"./bookshelf-BGr31l-A.js";const y="data:image/svg+xml,%3csvg%20width='120'%20height='160'%20viewBox='0%200%20120%20160'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3crect%20width='120'%20height='160'%20rx='8'%20fill='%23f5f5f5'%20stroke='%23e0e0e0'%20stroke-width='1'/%3e%3crect%20x='20'%20y='30'%20width='80'%20height='4'%20rx='2'%20fill='%23d0d0d0'/%3e%3crect%20x='20'%20y='45'%20width='60'%20height='3'%20rx='1.5'%20fill='%23e0e0e0'/%3e%3crect%20x='20'%20y='55'%20width='70'%20height='3'%20rx='1.5'%20fill='%23e0e0e0'/%3e%3crect%20x='35'%20y='80'%20width='50'%20height='50'%20rx='4'%20fill='%23e8e8e8'/%3e%3cpath%20d='M50%2095L60%20105L70%2095'%20stroke='%23c0c0c0'%20stroke-width='2'%20stroke-linecap='round'%20stroke-linejoin='round'/%3e%3c/svg%3e",Q={class:"bookshelf-overview"},U={class:"overview-header"},W={class:"header-actions"},X={class:"stats-section"},Y={class:"stats-grid"},Z={class:"stat-card"},tt={class:"stat-icon"},st={class:"stat-content"},et={class:"stat-number"},ot={class:"stat-card"},it={class:"stat-icon reading"},at={class:"stat-content"},lt={class:"stat-number"},nt={class:"stat-card"},dt={class:"stat-icon finished"},ct={class:"stat-content"},rt={class:"stat-number"},_t={class:"stat-card"},vt={class:"stat-icon favorite"},ut={class:"stat-content"},ht={class:"stat-number"},ft={class:"quick-actions-section"},gt={class:"quick-actions-grid"},kt={class:"action-icon"},pt={class:"action-icon"},mt={class:"action-icon"},wt={class:"action-icon"},yt={key:0,class:"recent-section"},bt={class:"section-header"},xt={class:"recent-books-grid"},Ct=["onClick"],Bt={class:"book-cover"},St=["src","alt"],Vt={class:"book-info"},Ft={class:"book-title"},Pt={class:"book-author"},Et={key:0,class:"book-progress"},It={class:"progress-text"},Lt={key:1,class:"favorites-section"},Mt={class:"section-header"},Nt={class:"favorite-books-grid"},Rt=["onClick"],qt={class:"book-cover"},At=["src","alt"],Dt={class:"favorite-badge"},Tt={class:"book-info"},$t={class:"book-title"},jt={class:"book-author"},Ot={key:2,class:"empty-state"},zt={class:"empty-icon"},Gt=D({__name:"BookshelfOverview",setup(Ht){const _=H(),f=K(),b=T(!1),v=p(()=>{const o=f.books;return{total:o.length,reading:o.filter(s=>s.readingStatus==="reading").length,finished:o.filter(s=>s.readingStatus==="finished").length,favorites:o.filter(s=>s.isFavorite).length}}),x=p(()=>f.books.filter(o=>o.lastReadAt).sort((o,s)=>new Date(s.lastReadAt).getTime()-new Date(o.lastReadAt).getTime()).slice(0,6)),C=p(()=>f.books.filter(o=>o.isFavorite).slice(0,6)),B=()=>{_.push("/library")},k=()=>{_.push("/import")},S=()=>{_.push("/search")},N=()=>{_.push("/bookmark")},R=()=>{_.push("/library?filter=favorites")},V=o=>{_.push(`/reader/${o.id}`)},F=o=>{const s=o.target;s.src=y};return $(async()=>{b.value=!0;try{await f.loadBooks()}catch(o){console.error("加载图书数据失败:",o)}finally{b.value=!1}}),(o,s)=>{const u=m("el-button"),n=m("el-icon"),q=m("el-progress");return r(),d("div",Q,[t("div",U,[s[2]||(s[2]=t("div",{class:"header-left"},[t("h1",{class:"page-title"},"我的书架"),t("p",{class:"page-subtitle"},"欢迎回来，继续您的阅读之旅")],-1)),t("div",W,[e(u,{type:"primary",icon:l(P),onClick:k},{default:a(()=>s[0]||(s[0]=[h(" 导入图书 ")])),_:1,__:[0]},8,["icon"]),e(u,{icon:l(E),onClick:S},{default:a(()=>s[1]||(s[1]=[h(" 搜索图书 ")])),_:1,__:[1]},8,["icon"])])]),t("div",X,[t("div",Y,[t("div",Z,[t("div",tt,[e(n,null,{default:a(()=>[e(l(I))]),_:1})]),t("div",st,[t("div",et,c(v.value.total),1),s[3]||(s[3]=t("div",{class:"stat-label"},"总图书",-1))])]),t("div",ot,[t("div",it,[e(n,null,{default:a(()=>[e(l(j))]),_:1})]),t("div",at,[t("div",lt,c(v.value.reading),1),s[4]||(s[4]=t("div",{class:"stat-label"},"正在阅读",-1))])]),t("div",nt,[t("div",dt,[e(n,null,{default:a(()=>[e(l(O))]),_:1})]),t("div",ct,[t("div",rt,c(v.value.finished),1),s[5]||(s[5]=t("div",{class:"stat-label"},"已完成",-1))])]),t("div",_t,[t("div",vt,[e(n,null,{default:a(()=>[e(l(w))]),_:1})]),t("div",ut,[t("div",ht,c(v.value.favorites),1),s[6]||(s[6]=t("div",{class:"stat-label"},"收藏图书",-1))])])])]),t("div",ft,[s[11]||(s[11]=t("h2",{class:"section-title"},"快速操作",-1)),t("div",gt,[t("div",{class:"action-card",onClick:B},[t("div",kt,[e(n,null,{default:a(()=>[e(l(z))]),_:1})]),s[7]||(s[7]=t("div",{class:"action-content"},[t("div",{class:"action-title"},"浏览图书"),t("div",{class:"action-desc"},"查看所有图书列表")],-1))]),t("div",{class:"action-card",onClick:k},[t("div",pt,[e(n,null,{default:a(()=>[e(l(G))]),_:1})]),s[8]||(s[8]=t("div",{class:"action-content"},[t("div",{class:"action-title"},"导入图书"),t("div",{class:"action-desc"},"添加新的电子书")],-1))]),t("div",{class:"action-card",onClick:S},[t("div",mt,[e(n,null,{default:a(()=>[e(l(E))]),_:1})]),s[9]||(s[9]=t("div",{class:"action-content"},[t("div",{class:"action-title"},"搜索图书"),t("div",{class:"action-desc"},"快速查找图书")],-1))]),t("div",{class:"action-card",onClick:N},[t("div",wt,[e(n,null,{default:a(()=>[e(l(w))]),_:1})]),s[10]||(s[10]=t("div",{class:"action-content"},[t("div",{class:"action-title"},"书签笔记"),t("div",{class:"action-desc"},"管理阅读标记")],-1))])])]),x.value.length>0?(r(),d("div",yt,[t("div",bt,[s[13]||(s[13]=t("h2",{class:"section-title"},"最近阅读",-1)),e(u,{text:"",onClick:B},{default:a(()=>s[12]||(s[12]=[h("查看全部")])),_:1,__:[12]})]),t("div",xt,[(r(!0),d(L,null,M(x.value,i=>(r(),d("div",{key:i.id,class:"book-card",onClick:A=>V(i)},[t("div",Bt,[t("img",{src:i.coverPath||l(y),alt:i.title,onError:F},null,40,St)]),t("div",Vt,[t("div",Ft,c(i.title),1),t("div",Pt,c(i.author||"未知作者"),1),i.readingProgress?(r(),d("div",Et,[e(q,{percentage:Math.round(i.readingProgress*100),"show-text":!1,"stroke-width":4},null,8,["percentage"]),t("span",It,c(Math.round(i.readingProgress*100))+"%",1)])):g("",!0)])],8,Ct))),128))])])):g("",!0),C.value.length>0?(r(),d("div",Lt,[t("div",Mt,[s[15]||(s[15]=t("h2",{class:"section-title"},"收藏图书",-1)),e(u,{text:"",onClick:R},{default:a(()=>s[14]||(s[14]=[h("查看全部")])),_:1,__:[14]})]),t("div",Nt,[(r(!0),d(L,null,M(C.value,i=>(r(),d("div",{key:i.id,class:"book-card",onClick:A=>V(i)},[t("div",qt,[t("img",{src:i.coverPath||l(y),alt:i.title,onError:F},null,40,At),t("div",Dt,[e(n,null,{default:a(()=>[e(l(w))]),_:1})])]),t("div",Tt,[t("div",$t,c(i.title),1),t("div",jt,c(i.author||"未知作者"),1)])],8,Rt))),128))])])):g("",!0),v.value.total===0?(r(),d("div",Ot,[t("div",zt,[e(n,null,{default:a(()=>[e(l(I))]),_:1})]),s[17]||(s[17]=t("div",{class:"empty-title"},"书架还是空的",-1)),s[18]||(s[18]=t("div",{class:"empty-desc"},"开始导入您的第一本电子书吧",-1)),e(u,{type:"primary",icon:l(P),onClick:k},{default:a(()=>s[16]||(s[16]=[h(" 导入图书 ")])),_:1,__:[16]},8,["icon"])])):g("",!0)])}}}),Qt=J(Gt,[["__scopeId","data-v-cea135b1"]]);export{Qt as default};
