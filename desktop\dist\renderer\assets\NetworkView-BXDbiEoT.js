import{d as t,o as a,a as n,b as e,f as c,h as r,x as p,_}from"./index-N2UppB4L.js";const d={class:"network-view"},i={class:"network-content"},l=t({__name:"NetworkView",setup(m){return a(()=>{console.log("网络设置页面已加载")}),(f,o)=>{const s=r("el-empty");return p(),n("div",d,[o[0]||(o[0]=e("div",{class:"page-header"},[e("h1",{class:"page-title"},"网络设置"),e("p",{class:"page-description"},"配置网络连接")],-1)),e("div",i,[c(s,{description:"网络设置功能开发中..."})])])}}}),k=_(l,[["__scopeId","data-v-f678277a"]]);export{k as default};
