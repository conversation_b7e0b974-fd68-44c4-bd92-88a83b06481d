import{d as o,o as n,a,b as e,f as c,h as p,x as i,_ as r}from"./index-BvDCFwwL.js";const _={class:"experiments-view"},d={class:"experiments-content"},l=o({__name:"ExperimentsView",setup(m){return n(()=>{console.log("实验任务页面已加载")}),(x,s)=>{const t=p("el-empty");return i(),a("div",_,[s[0]||(s[0]=e("div",{class:"page-header"},[e("h1",{class:"page-title"},"实验任务"),e("p",{class:"page-description"},"进行实践操作和实验")],-1)),e("div",d,[c(t,{description:"实验任务功能开发中..."})])])}}}),v=r(l,[["__scopeId","data-v-1cc918b8"]]);export{v as default};
