import{d as t,o as a,a as n,b as e,f as c,h as p,x as d,_}from"./index-N2UppB4L.js";const i={class:"theme-view"},r={class:"theme-content"},l=t({__name:"ThemeView",setup(m){return a(()=>{console.log("主题设置页面已加载")}),(h,s)=>{const o=p("el-empty");return d(),n("div",i,[s[0]||(s[0]=e("div",{class:"page-header"},[e("h1",{class:"page-title"},"主题设置"),e("p",{class:"page-description"},"外观和主题配置")],-1)),e("div",r,[c(o,{description:"主题设置功能开发中..."})])])}}}),v=_(l,[["__scopeId","data-v-d2eced58"]]);export{v as default};
