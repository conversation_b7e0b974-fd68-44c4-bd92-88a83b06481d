/**
 * 导航菜单配置
 * 定义5大功能模块的菜单结构和快速操作
 */

import type { Component } from 'vue'

// 菜单项接口
export interface MenuItem {
  path: string
  title: string
  description?: string
  icon: string
  children?: MenuItem[]
  badge?: string | number
  shortcut?: string
}

// 快速操作接口
export interface QuickAction {
  id: string
  title: string
  description?: string
  icon: string
  shortcut?: string
  action: () => void
}

// 主导航菜单配置
export const mainNavigationItems: MenuItem[] = [
  // 1. 我的书架（父菜单）
  {
    path: '/bookshelf',
    title: '我的书架',
    description: '查看书架概览和阅读统计',
    icon: 'Collection',
    children: [
      // 1.1 图书列表
      {
        path: '/library',
        title: '图书列表',
        description: '浏览所有图书',
        icon: 'Grid'
      },
      // 1.2 导入图书
      {
        path: '/import',
        title: '导入图书',
        description: '添加新的电子书',
        icon: 'Upload'
      },
      // 1.3 搜索图书
      {
        path: '/search',
        title: '搜索图书',
        description: '快速查找图书',
        icon: 'Search'
      }
    ]
  },

  // 5. 学习服务
  {
    path: '/learning',
    title: '学习服务',
    description: '智能学习辅助功能',
    icon: 'Reading',
    badge: '新',
    children: [
      {
        path: '/learning/tasks',
        title: '学习任务',
        description: '查看学习进度',
        icon: 'List'
      },
      {
        path: '/learning/homework',
        title: '作业任务',
        description: '完成课后作业',
        icon: 'EditPen',
        badge: 3
      },
      {
        path: '/learning/tests',
        title: '测试任务',
        description: '参与在线测试',
        icon: 'Document'
      },
      {
        path: '/learning/experiments',
        title: '实验任务',
        description: '进行实践操作',
        icon: 'Operation'
      }
    ]
  },

  // 6. 书签笔记
  {
    path: '/bookmark',
    title: '书签笔记',
    description: '管理阅读标记和笔记',
    icon: 'Star',
    children: [
      {
        path: '/bookmark/bookmarks',
        title: '书签管理',
        description: '查看所有书签',
        icon: 'Star'
      },
      {
        path: '/bookmark/notes',
        title: '笔记管理',
        description: '整理阅读笔记',
        icon: 'EditPen'
      },
      {
        path: '/bookmark/highlights',
        title: '高亮管理',
        description: '管理文本高亮',
        icon: 'Brush'
      }
    ]
  },

  // 7. 设置
  {
    path: '/settings',
    title: '设置',
    description: '个性化配置选项',
    icon: 'Setting',
    children: [
      {
        path: '/settings/general',
        title: '通用设置',
        description: '基本应用配置',
        icon: 'Tools'
      },
      {
        path: '/settings/reading',
        title: '阅读设置',
        description: '阅读体验配置',
        icon: 'View'
      },
      {
        path: '/settings/theme',
        title: '主题设置',
        description: '外观和主题配置',
        icon: 'Brush'
      }
    ]
  },

  // 8. 个人中心
  {
    path: '/profile',
    title: '个人中心',
    description: '用户信息和统计',
    icon: 'User',
    children: [
      {
        path: '/profile/reports',
        title: '学习报告',
        description: '查看学习统计',
        icon: 'DataAnalysis'
      },
      {
        path: '/profile/account',
        title: '账号设置',
        description: '管理账号信息',
        icon: 'UserFilled'
      },
      {
        path: '/profile/network',
        title: '网络设置',
        description: '配置网络连接',
        icon: 'Connection'
      }
    ]
  }
]

// 快速操作配置
export const quickActions: QuickAction[] = [
  {
    id: 'import-book',
    title: '导入图书',
    description: '快速添加新书',
    icon: 'Upload',
    shortcut: 'Ctrl+I',
    action: () => {
      // 触发导入图书对话框
      console.log('导入图书')
    }
  },
  {
    id: 'search-global',
    title: '全局搜索',
    description: '搜索所有内容',
    icon: 'Search',
    shortcut: 'Ctrl+F',
    action: () => {
      // 触发全局搜索
      console.log('全局搜索')
    }
  },
  {
    id: 'recent-books',
    title: '最近阅读',
    description: '快速访问最近的书',
    icon: 'Clock',
    shortcut: 'Ctrl+R',
    action: () => {
      // 显示最近阅读列表
      console.log('最近阅读')
    }
  }
]

// 获取菜单项的图标组件
export const getMenuIcon = (iconName: string): Component | string => {
  // 这里可以根据图标名称返回对应的组件
  // 暂时返回字符串，实际使用时需要导入对应的图标组件
  return iconName
}

// 根据路径查找菜单项
export const findMenuItemByPath = (path: string, items: MenuItem[] = mainNavigationItems): MenuItem | null => {
  for (const item of items) {
    if (item.path === path) {
      return item
    }
    if (item.children) {
      const found = findMenuItemByPath(path, item.children)
      if (found) return found
    }
  }
  return null
}

// 获取菜单项的面包屑路径
export const getMenuBreadcrumbs = (path: string): MenuItem[] => {
  const breadcrumbs: MenuItem[] = []
  
  const findPath = (items: MenuItem[], targetPath: string, currentPath: MenuItem[] = []): boolean => {
    for (const item of items) {
      const newPath = [...currentPath, item]
      
      if (item.path === targetPath) {
        breadcrumbs.push(...newPath)
        return true
      }
      
      if (item.children && findPath(item.children, targetPath, newPath)) {
        return true
      }
    }
    return false
  }
  
  findPath(mainNavigationItems, path)
  return breadcrumbs
}

// 检查菜单项是否有权限访问
export const hasMenuPermission = (item: MenuItem): boolean => {
  // 这里可以添加权限检查逻辑
  // 暂时返回 true，表示所有菜单都可访问
  return true
}

// 过滤有权限的菜单项
export const getAuthorizedMenuItems = (items: MenuItem[] = mainNavigationItems): MenuItem[] => {
  return items.filter(item => {
    if (!hasMenuPermission(item)) return false
    
    if (item.children) {
      item.children = getAuthorizedMenuItems(item.children)
    }
    
    return true
  })
}

// 获取扁平化的菜单项列表（用于搜索等功能）
export const getFlatMenuItems = (items: MenuItem[] = mainNavigationItems): MenuItem[] => {
  const flatItems: MenuItem[] = []
  
  const flatten = (menuItems: MenuItem[]) => {
    for (const item of menuItems) {
      flatItems.push(item)
      if (item.children) {
        flatten(item.children)
      }
    }
  }
  
  flatten(items)
  return flatItems
}

// 菜单项搜索
export const searchMenuItems = (query: string, items: MenuItem[] = mainNavigationItems): MenuItem[] => {
  const results: MenuItem[] = []
  const flatItems = getFlatMenuItems(items)
  
  const searchTerm = query.toLowerCase()
  
  for (const item of flatItems) {
    if (
      item.title.toLowerCase().includes(searchTerm) ||
      item.description?.toLowerCase().includes(searchTerm)
    ) {
      results.push(item)
    }
  }
  
  return results
}
