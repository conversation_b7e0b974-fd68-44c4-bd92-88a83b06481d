import{T as X,r as l,c as f}from"./index-N2UppB4L.js";const b=X("bookshelf",()=>{const n=l([]),h=l(!1),w=l(null),A=l("grid"),m=l("addedAt"),i=l("desc"),d=l(20),u=l(1),r=l({search:"",author:"",formats:[],category:"",isFavorite:void 0,language:"",readingStatus:"all",tags:[],dateRange:{}}),o=l(new Set),v=l(!1),S=l(null),P=l(!1),k=l([]),B=l([]),R=l([]),g=f(()=>{let e=[...n.value||[]];if(r.value.search){const a=r.value.search.toLowerCase();e=e.filter(t=>t.title.toLowerCase().includes(a)||t.author.toLowerCase().includes(a)||t.description&&t.description.toLowerCase().includes(a))}if(r.value.author&&(e=e.filter(a=>a.author.toLowerCase().includes(r.value.author.toLowerCase()))),r.value.formats.length>0&&(e=e.filter(a=>r.value.formats.includes(a.format))),r.value.category&&(e=e.filter(a=>a.category===r.value.category)),r.value.isFavorite!==void 0&&(e=e.filter(a=>a.isFavorite===r.value.isFavorite)),r.value.language&&(e=e.filter(a=>a.language===r.value.language)),r.value.readingStatus!=="all")switch(r.value.readingStatus){case"unread":e=e.filter(a=>a.readProgress===0);break;case"reading":e=e.filter(a=>a.readProgress>0&&a.readProgress<100);break;case"finished":e=e.filter(a=>a.readProgress===100);break}return r.value.tags.length>0&&(e=e.filter(a=>{if(!a.tags)return!1;try{const t=JSON.parse(a.tags);return r.value.tags.some(s=>t.includes(s))}catch{return!1}})),r.value.dateRange.start&&(e=e.filter(a=>a.addedAt>=r.value.dateRange.start)),r.value.dateRange.end&&(e=e.filter(a=>a.addedAt<=r.value.dateRange.end)),e}),C=f(()=>{const e=[...g.value];return e.sort((a,t)=>{let s,c;switch(m.value){case"title":s=a.title.toLowerCase(),c=t.title.toLowerCase();break;case"author":s=a.author.toLowerCase(),c=t.author.toLowerCase();break;case"addedAt":s=new Date(a.addedAt).getTime(),c=new Date(t.addedAt).getTime();break;case"lastReadAt":s=a.lastReadAt?new Date(a.lastReadAt).getTime():0,c=t.lastReadAt?new Date(t.lastReadAt).getTime():0;break;case"readProgress":s=a.readProgress,c=t.readProgress;break;case"fileSize":s=a.fileSize,c=t.fileSize;break;default:s=a.addedAt,c=t.addedAt}return s<c?i.value==="asc"?-1:1:s>c?i.value==="asc"?1:-1:0}),e}),y=f(()=>{const e=(u.value-1)*d.value,a=e+d.value;return C.value.slice(e,a)}),T=f(()=>{const e=n.value||[],a=g.value||[];return{total:e.length,filtered:a.length,unread:e.filter(t=>t.readProgress===0).length,reading:e.filter(t=>t.readProgress>0&&t.readProgress<100).length,finished:e.filter(t=>t.readProgress===100).length,favorites:e.filter(t=>t.isFavorite).length,selected:o.value.size}}),F=f(()=>({current:u.value,pageSize:d.value,total:g.value.length,totalPages:Math.ceil(g.value.length/d.value)})),D=async(e=!1)=>{if(!(h.value&&!e))try{h.value=!0,w.value=null,n.value=[]}catch(a){w.value=a instanceof Error?a.message:"加载图书列表失败",console.error("加载图书列表失败:",a)}finally{h.value=!1}},I=async e=>{r.value.search=e,u.value=1},x=e=>{Object.assign(r.value,e),u.value=1},E=()=>{r.value={search:"",author:"",formats:[],category:"",isFavorite:void 0,language:"",readingStatus:"all",tags:[],dateRange:{}},u.value=1},M=(e,a)=>{m.value===e&&!a?i.value=i.value==="asc"?"desc":"asc":(m.value=e,i.value=a||"asc"),u.value=1},O=e=>{A.value=e},V=e=>{d.value=e,u.value=1},j=e=>{e>=1&&e<=F.value.totalPages&&(u.value=e)},J=e=>{o.value.has(e)?o.value.delete(e):o.value.add(e)},N=()=>{o.value.size===y.value.length?o.value.clear():(o.value.clear(),y.value.forEach(e=>o.value.add(e.id)))},q=()=>{o.value.clear(),v.value=!1},G=()=>{v.value=!v.value,v.value||o.value.clear()},L=e=>{n.value.unshift(e)},p=(e,a)=>{const t=n.value.findIndex(s=>s.id===e);t!==-1&&(n.value[t]={...n.value[t],...a})},H=e=>{const a=n.value.findIndex(t=>t.id===e);a!==-1&&n.value.splice(a,1),o.value.delete(e)},K=e=>{n.value=n.value.filter(a=>!e.includes(a.id)),e.forEach(a=>o.value.delete(a))},Q=async(e,a)=>{try{p(e,{isFavorite:a})}catch(t){throw console.error("设置收藏状态失败:",t),t}},U=async(e,a)=>{try{e.forEach(t=>p(t,{isFavorite:a}))}catch(t){throw console.error("批量设置收藏状态失败:",t),t}},W=async(e,a,t)=>{try{const s={readProgress:a,lastReadAt:new Date().toISOString()};t!==void 0&&(s.currentPage=t),p(e,s)}catch(s){throw console.error("更新阅读进度失败:",s),s}},z=async()=>{try{k.value=[],B.value=[],R.value=[]}catch(e){console.error("加载缓存数据失败:",e)}};return{books:n,loading:h,error:w,viewMode:A,sortField:m,sortOrder:i,pageSize:d,currentPage:u,filter:r,selectedBooks:o,isSelectionMode:v,importProgress:S,isImporting:P,categories:k,tags:B,authors:R,filteredBooks:g,sortedBooks:C,paginatedBooks:y,stats:T,pagination:F,loadBooks:D,searchBooks:I,setFilter:x,clearFilter:E,setSort:M,setViewMode:O,setPageSize:V,goToPage:j,toggleBookSelection:J,toggleSelectAll:N,clearSelection:q,toggleSelectionMode:G,addBook:L,updateBook:p,removeBook:H,removeBooks:K,setFavorite:Q,setBatchFavorite:U,updateProgress:W,loadCacheData:z,startImport:()=>{P.value=!0,S.value={total:0,completed:0,current:"",percentage:0,status:"pending",errors:[]}},updateImportProgress:e=>{S.value=e},finishImport:e=>{P.value=!1,e.imported.forEach(a=>L(a)),z()}}});export{b as u};
