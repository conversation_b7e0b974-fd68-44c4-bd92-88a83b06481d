<template>
  <div class="simple-import-view">
    <div class="import-header">
      <h1 class="page-title">导入图书</h1>
      <p class="page-description">选择电子书文件进行导入</p>
    </div>

    <div class="import-content">
      <!-- 文件选择区域 -->
      <div class="file-selector">
        <el-card>
          <template #header>
            <span>选择图书文件</span>
          </template>
          
          <div class="selector-content">
            <el-button 
              type="primary" 
              :icon="Upload" 
              @click="selectFiles"
              :loading="importing"
              size="large"
            >
              选择文件
            </el-button>
            
            <p class="supported-formats">
              支持格式：TXT, EPUB, PDF, MOBI, AZW3
            </p>
          </div>
        </el-card>
      </div>

      <!-- 导入进度 -->
      <div v-if="importing" class="import-progress">
        <el-card>
          <template #header>
            <span>导入进度</span>
          </template>
          
          <div class="progress-content">
            <el-progress 
              :percentage="importProgress" 
              :status="importStatus"
            />
            <p class="progress-text">{{ progressText }}</p>
          </div>
        </el-card>
      </div>

      <!-- 导入结果 -->
      <div v-if="importResults.length > 0" class="import-results">
        <el-card>
          <template #header>
            <span>导入结果</span>
          </template>
          
          <div class="results-content">
            <div v-for="result in importResults" :key="result.file" class="result-item">
              <el-icon v-if="result.success" class="success-icon" color="#67C23A">
                <SuccessFilled />
              </el-icon>
              <el-icon v-else class="error-icon" color="#F56C6C">
                <Warning />
              </el-icon>
              <span class="result-text">{{ result.message }}</span>
            </div>
          </div>
        </el-card>
      </div>

      <!-- 返回按钮 -->
      <div class="actions">
        <el-button @click="goBack">返回图书列表</el-button>
        <el-button v-if="importResults.length > 0" type="primary" @click="goBack">
          查看导入的图书
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Upload, SuccessFilled, Warning } from '@element-plus/icons-vue'

const router = useRouter()

// 响应式数据
const importing = ref(false)
const importProgress = ref(0)
const importStatus = ref<'success' | 'exception' | undefined>(undefined)
const progressText = ref('')
const importResults = ref<Array<{file: string, success: boolean, message: string}>>([])

// 选择文件
const selectFiles = async () => {
  try {
    // 使用正确的文件选择 API
    const filePaths = await window.electronAPI.file.select([
      { name: '电子书文件', extensions: ['txt', 'epub', 'pdf', 'mobi', 'azw3'] },
      { name: '文本文件', extensions: ['txt'] },
      { name: 'EPUB文件', extensions: ['epub'] },
      { name: 'PDF文件', extensions: ['pdf'] },
      { name: '所有文件', extensions: ['*'] }
    ])

    if (filePaths && filePaths.length > 0) {
      await importFiles(filePaths)
    }
  } catch (error) {
    console.error('选择文件失败:', error)
    ElMessage.error('选择文件失败')
  }
}

// 导入文件
const importFiles = async (filePaths: string[]) => {
  importing.value = true
  importProgress.value = 0
  importStatus.value = undefined
  importResults.value = []
  progressText.value = '开始导入...'

  try {
    for (let i = 0; i < filePaths.length; i++) {
      const filePath = filePaths[i]
      const fileName = filePath.split(/[/\\]/).pop() || filePath
      
      progressText.value = `正在导入: ${fileName}`
      importProgress.value = Math.round((i / filePaths.length) * 100)

      try {
        // 调用正确的添加图书API
        const result = await window.electronAPI.book.add(filePath)
        
        importResults.value.push({
          file: fileName,
          success: true,
          message: `${fileName} 导入成功`
        })
        
        ElMessage.success(`${fileName} 导入成功`)
      } catch (error) {
        console.error(`导入 ${fileName} 失败:`, error)
        
        importResults.value.push({
          file: fileName,
          success: false,
          message: `${fileName} 导入失败: ${error.message || '未知错误'}`
        })
        
        ElMessage.error(`${fileName} 导入失败`)
      }
    }

    importProgress.value = 100
    importStatus.value = 'success'
    progressText.value = '导入完成'
    
  } catch (error) {
    console.error('导入过程出错:', error)
    importStatus.value = 'exception'
    progressText.value = '导入失败'
    ElMessage.error('导入过程出错')
  } finally {
    importing.value = false
  }
}

// 返回图书列表
const goBack = () => {
  router.push('/library')
}
</script>

<style scoped>
.simple-import-view {
  padding: 24px;
  max-width: 800px;
  margin: 0 auto;
}

.import-header {
  text-align: center;
  margin-bottom: 32px;
}

.page-title {
  font-size: 28px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 8px 0;
}

.page-description {
  font-size: 16px;
  color: #606266;
  margin: 0;
}

.import-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.selector-content {
  text-align: center;
  padding: 32px;
}

.supported-formats {
  margin-top: 16px;
  color: #909399;
  font-size: 14px;
}

.progress-content {
  padding: 16px;
}

.progress-text {
  margin-top: 12px;
  text-align: center;
  color: #606266;
}

.results-content {
  padding: 16px;
}

.result-item {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.result-text {
  font-size: 14px;
}

.actions {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-top: 24px;
}
</style>
