import{d as Ge,y as Ke,r as D,c as Qe,z as ce,o as qe,a as u,b as s,e as m,t as r,u as n,f as t,w as a,g as d,p as He,h as c,A as Je,s as Oe,B as X,F as P,q as U,C as z,D as We,E as Xe,m as Ye,G as Ze,H as et,I as tt,J as k,j as _e,K as ge,L as pe,v as lt,M as v,N as at,x as i,_ as ot}from"./index-N2UppB4L.js";import{u as nt}from"./unifiedLibrary-Chw4J5Va.js";const st={class:"unified-library-view"},it={class:"library-header"},rt={class:"header-left"},dt={class:"stats-info"},ut={key:0},ct={key:1},_t={class:"header-actions"},gt={class:"toolbar"},pt={class:"toolbar-left"},ft={class:"toolbar-right"},mt={class:"result-count"},vt={key:0,class:"initialization-container"},ht={class:"initialization-content"},yt={class:"initialization-progress"},kt={key:1,class:"retry-container"},wt={class:"retry-content"},bt={class:"retry-progress"},Ct={key:2,class:"loading-container"},zt={key:3,class:"error-container"},xt={class:"debug-info",style:{background:"#ffe6e6",padding:"10px",margin:"10px 0","font-size":"12px",border:"1px solid #ff9999"}},Bt={class:"error-actions"},St={key:0,class:"retry-info"},Vt={key:4,class:"empty-container"},Pt={key:5,class:"books-grid"},Ut=["onClick"],Et={class:"book-cover"},Mt=["src","alt"],$t={key:1,class:"default-cover"},Rt={class:"format-badge"},At={key:2,class:"progress-overlay"},Ft={class:"book-info"},It=["title"],Lt=["title"],Tt={class:"book-meta"},Dt={class:"file-size"},Nt={key:0,class:"book-tags"},jt={key:0,class:"more-tags"},Gt={class:"book-actions"},Kt={key:6,class:"books-list"},Qt={class:"list-cover"},qt=["src","alt"],Ht={key:1,class:"default-list-cover"},Jt={class:"book-title-cell"},Ot={key:0,class:"book-description"},Wt={key:7,class:"pagination-container"},B=3,Xt=Ge({__name:"UnifiedLibraryView",setup(Yt){const S=lt(),Y=nt(),{books:x,loading:E,error:M,searchQuery:N,filter:j,sortField:fe,sortOrder:Zt,viewMode:G,pageSize:K,currentPage:Q,paginatedBooks:w,filteredBooks:q,stats:V,availableFormats:me,availableAuthors: <AUTHORS>
