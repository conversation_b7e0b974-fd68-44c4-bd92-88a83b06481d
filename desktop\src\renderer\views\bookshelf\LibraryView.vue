<!--
  图书列表页面
  显示所有图书的网格或列表视图，支持搜索、筛选、排序等功能
-->
<template>
  <div class="library-view">
    <!-- 页面头部 -->
    <div class="library-header">
      <div class="header-left">
        <h1 class="page-title">图书列表</h1>
        <div class="stats-info">
          <span>共 {{ stats.total }} 本图书</span>
          <span v-if="stats.filtered !== stats.total">，筛选后 {{ stats.filtered }} 本</span>
        </div>
      </div>

      <div class="header-actions">
        <!-- 搜索框 -->
        <el-input
          :model-value="filter.search"
          @update:model-value="handleSearch"
          placeholder="搜索图书标题、作者、出版社或标签..."
          class="search-input"
          clearable
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>

        <!-- 筛选控件排成一排 -->
        <div class="filter-controls">
          <!-- 全部格式 -->
          <el-select
            :model-value="filter.formats.length === 0 ? 'all' : filter.formats[0]"
            @update:model-value="handleFormatChange"
            placeholder="全部格式"
            style="width: 120px"
          >
            <el-option label="全部格式" value="all" />
            <el-option label="EPUB" value="epub" />
            <el-option label="PDF" value="pdf" />
            <el-option label="TXT" value="txt" />
            <el-option label="MOBI" value="mobi" />
            <el-option label="AZW3" value="azw3" />
          </el-select>

          <!-- 全部状态 -->
          <el-select
            :model-value="filter.readingStatus"
            @update:model-value="updateFilter('readingStatus', $event)"
            placeholder="全部状态"
            style="width: 120px"
          >
            <el-option label="全部状态" value="all" />
            <el-option label="未开始" value="unread" />
            <el-option label="阅读中" value="reading" />
            <el-option label="已完成" value="completed" />
          </el-select>

          <!-- 全部作者 -->
          <el-select
            :model-value="filter.author"
            @update:model-value="updateFilter('author', $event)"
            placeholder="全部作者"
            style="width: 120px"
            filterable
            clearable
          >
            <el-option label="全部作者" value="" />
            <el-option
              v-for="author in uniqueAuthors"
              :key="author"
              :label="author"
              :value="author"
            />
          </el-select>

          <!-- 全部出版社 -->
          <el-select
            :model-value="filter.publisher"
            @update:model-value="updateFilter('publisher', $event)"
            placeholder="全部出版社"
            style="width: 120px"
            filterable
            clearable
          >
            <el-option label="全部出版社" value="" />
            <el-option
              v-for="publisher in uniquePublishers"
              :key="publisher"
              :label="publisher"
              :value="publisher"
            />
          </el-select>

          <!-- 清除筛选 -->
          <el-button
            v-if="hasActiveFilters"
            @click="clearFilter"
            size="small"
            type="info"
            plain
          >
            <el-icon><Close /></el-icon>
            清除筛选
          </el-button>
        </div>

        <!-- 操作按钮组 -->
        <div class="action-controls">
          <!-- 排序选择 -->
          <el-select
            :model-value="sortField"
            @update:model-value="handleSortChange"
            placeholder="排序方式"
            style="width: 120px"
          >
            <el-option label="添加时间" value="addedAt" />
            <el-option label="最后阅读" value="lastReadAt" />
            <el-option label="书名" value="title" />
            <el-option label="作者" value="author" />
            <el-option label="阅读进度" value="readProgress" />
            <el-option label="文件大小" value="fileSize" />
          </el-select>

          <!-- 排序方向 -->
          <el-button @click="toggleSortOrder">
            <el-icon>
              <component :is="sortOrder === 'asc' ? SortUp : SortDown" />
            </el-icon>
            <span>{{ sortOrder === 'asc' ? '升序' : '降序' }}</span>
          </el-button>

          <!-- 导入图书 -->
          <el-button type="primary" @click="handleImport">
            <el-icon><Plus /></el-icon>
            导入图书
          </el-button>

          <!-- 视图切换 -->
          <el-button-group>
            <el-button
              :type="viewMode === 'grid' ? 'primary' : 'default'"
              @click="setViewMode('grid')"
            >
              <el-icon><Grid /></el-icon>
            </el-button>
            <el-button
              :type="viewMode === 'list' ? 'primary' : 'default'"
              @click="setViewMode('list')"
            >
              <el-icon><List /></el-icon>
            </el-button>
          </el-button-group>

          <!-- 批量操作 -->
          <el-button
            v-if="!isSelectionMode"
            @click="toggleSelectionMode"
          >
            <el-icon><Select /></el-icon>
            批量操作
          </el-button>

          <!-- 批量操作选择状态 -->
          <div v-if="isSelectionMode" class="selection-actions">
            <span class="selection-count">已选择 {{ selectedBooks.size }} 本</span>
            <el-button size="small" @click="toggleSelectAll">
              {{ selectedBooks.size === paginatedBooks.length ? '取消全选' : '全选' }}
            </el-button>
            <el-button size="small" type="danger" @click="handleBatchDelete">
              删除
            </el-button>
            <el-button size="small" @click="handleBatchFavorite">
              收藏
            </el-button>
            <el-button size="small" @click="clearSelection">
              取消
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 批量操作组件 -->
    <BatchOperations
      :selected-books="selectedBooks"
      :books="books"
      @clear-selection="clearSelection"
      @books-updated="handleBooksUpdated"
    />

    <!-- 主要内容区域 -->
    <div class="library-content">
      <!-- 加载状态 -->
      <div v-if="loading" class="loading-container">
        <el-skeleton :rows="6" animated />
      </div>

      <!-- 空状态 -->
      <div v-else-if="paginatedBooks.length === 0" class="empty-state">
        <el-empty description="暂无图书">
          <el-button type="primary" @click="handleImport">
            导入图书
          </el-button>
        </el-empty>
      </div>

      <!-- 网格视图 -->
      <div v-else-if="viewMode === 'grid'" class="books-grid">
        <div
          v-for="book in paginatedBooks"
          :key="book.id"
          class="book-card"
          :class="{ 'selected': selectedBooks.has(book.id) }"
          @click="handleBookClick(book)"
        >
          <!-- 选择框 -->
          <el-checkbox
            v-if="isSelectionMode"
            :model-value="selectedBooks.has(book.id)"
            @change="toggleBookSelection(book.id)"
            class="book-checkbox"
            @click.stop
          />

          <!-- 收藏标记 -->
          <el-icon
            v-if="book.isFavorite"
            class="favorite-icon"
            color="#f56c6c"
          >
            <Star />
          </el-icon>

          <div class="book-cover">
            <img
              :src="book.coverPath || placeholderImage"
              :alt="book.title"
              @error="handleImageError"
            />
            <div class="book-overlay">
              <el-button type="primary" size="small">
                <el-icon><View /></el-icon>
                打开
              </el-button>
            </div>

            <!-- 阅读进度 -->
            <div v-if="book.readProgress > 0" class="progress-bar">
              <div
                class="progress-fill"
                :style="{ width: book.readProgress + '%' }"
              />
            </div>
          </div>

          <div class="book-info">
            <h3 :title="book.title">{{ book.title }}</h3>
            <p :title="book.author">{{ book.author }}</p>
            <div class="book-meta">
              <span class="file-type">{{ book.format.toUpperCase() }}</span>
              <span class="file-size">{{ formatFileSize(book.fileSize) }}</span>
            </div>
            <div v-if="book.readProgress > 0" class="read-progress">
              已读 {{ book.readProgress }}%
            </div>
          </div>

          <!-- 快捷操作 -->
          <div class="book-actions">
            <el-dropdown trigger="click" @click.stop>
              <el-button size="small" circle>
                <el-icon><MoreFilled /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item @click="handleBookDetail(book)">
                    <el-icon><InfoFilled /></el-icon>
                    详情
                  </el-dropdown-item>
                  <el-dropdown-item @click="setFavorite(book.id, !book.isFavorite)">
                    <el-icon><Star /></el-icon>
                    {{ book.isFavorite ? '取消收藏' : '收藏' }}
                  </el-dropdown-item>
                  <el-dropdown-item @click="handleBookEdit(book)">
                    <el-icon><Edit /></el-icon>
                    编辑
                  </el-dropdown-item>
                  <el-dropdown-item divided @click="handleBookDelete(book)">
                    <el-icon><Delete /></el-icon>
                    删除
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>
      </div>

      <!-- 列表视图 -->
      <div v-else class="books-list">
        <el-table
          :data="paginatedBooks"
          @row-click="handleBookClick"
          :row-class-name="getRowClassName"
        >
          <!-- 选择列 -->
          <el-table-column v-if="isSelectionMode" type="selection" width="55" />

          <!-- 封面列 -->
          <el-table-column label="封面" width="80">
            <template #default="{ row }">
              <div class="table-cover">
                <img
                  :src="row.coverPath || placeholderImage"
                  :alt="row.title"
                  @error="handleImageError"
                />
              </div>
            </template>
          </el-table-column>

          <!-- 书名列 -->
          <el-table-column prop="title" label="书名" min-width="200" sortable>
            <template #default="{ row }">
              <div class="book-title-cell">
                <span class="title">{{ row.title }}</span>
                <el-icon v-if="row.isFavorite" class="favorite-icon" color="#f56c6c">
                  <Star />
                </el-icon>
              </div>
            </template>
          </el-table-column>

          <!-- 作者列 -->
          <el-table-column prop="author" label="作者" width="150" sortable />

          <!-- 格式列 -->
          <el-table-column label="格式" width="80">
            <template #default="{ row }">
              <el-tag size="small">{{ row.format.toUpperCase() }}</el-tag>
            </template>
          </el-table-column>

          <!-- 大小列 -->
          <el-table-column label="大小" width="100" sortable="custom">
            <template #default="{ row }">
              {{ formatFileSize(row.fileSize) }}
            </template>
          </el-table-column>

          <!-- 阅读进度列 -->
          <el-table-column label="进度" width="120">
            <template #default="{ row }">
              <el-progress
                :percentage="row.readProgress"
                :stroke-width="6"
                :show-text="false"
              />
              <span class="progress-text">{{ row.readProgress }}%</span>
            </template>
          </el-table-column>

          <!-- 最后阅读列 -->
          <el-table-column label="最后阅读" width="150" sortable="custom">
            <template #default="{ row }">
              {{ formatDate(row.lastReadAt) }}
            </template>
          </el-table-column>

          <!-- 操作列 -->
          <el-table-column label="操作" width="120" fixed="right">
            <template #default="{ row }">
              <el-button type="primary" size="small" @click.stop="handleBookClick(row)">
                打开
              </el-button>
              <el-dropdown trigger="click" @click.stop>
                <el-button size="small" circle>
                  <el-icon><MoreFilled /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item @click="handleBookDetail(row)">详情</el-dropdown-item>
                    <el-dropdown-item @click="setFavorite(row.id, !row.isFavorite)">
                      {{ row.isFavorite ? '取消收藏' : '收藏' }}
                    </el-dropdown-item>
                    <el-dropdown-item @click="handleBookEdit(row)">编辑</el-dropdown-item>
                    <el-dropdown-item divided @click="handleBookDelete(row)">删除</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 分页 -->
      <div v-if="pagination.totalPages > 1" class="pagination-container">
        <el-pagination
          :current-page="pagination.current"
          :page-size="pagination.pageSize"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="setPageSize"
          @current-change="goToPage"
        />
      </div>
    </div>

    <!-- 筛选抽屉 -->
    <el-drawer
      v-model="showFilterDrawer"
      title="筛选条件"
      direction="rtl"
      size="400px"
    >
      <div class="filter-content">
        <!-- 作者筛选 -->
        <div class="filter-group">
          <label>作者</label>
          <el-input
            :model-value="filter.author"
            @update:model-value="updateFilter('author', $event)"
            placeholder="输入作者名称"
            clearable
          />
        </div>

        <!-- 格式筛选 -->
        <div class="filter-group">
          <label>文件格式</label>
          <el-checkbox-group
            :model-value="filter.formats"
            @update:model-value="updateFilter('formats', $event)"
          >
            <el-checkbox value="epub">EPUB</el-checkbox>
            <el-checkbox value="pdf">PDF</el-checkbox>
            <el-checkbox value="txt">TXT</el-checkbox>
            <el-checkbox value="mobi">MOBI</el-checkbox>
            <el-checkbox value="azw3">AZW3</el-checkbox>
          </el-checkbox-group>
        </div>

        <!-- 分类筛选 -->
        <div class="filter-group">
          <label>分类</label>
          <el-select
            :model-value="filter.category"
            @update:model-value="updateFilter('category', $event)"
            placeholder="选择分类"
            clearable
          >
            <el-option
              v-for="category in categories"
              :key="category"
              :label="category"
              :value="category"
            />
          </el-select>
        </div>

        <!-- 收藏状态 -->
        <div class="filter-group">
          <label>收藏状态</label>
          <el-radio-group
            :model-value="filter.isFavorite"
            @update:model-value="updateFilter('isFavorite', $event)"
          >
            <el-radio :value="undefined">全部</el-radio>
            <el-radio :value="true">已收藏</el-radio>
            <el-radio :value="false">未收藏</el-radio>
          </el-radio-group>
        </div>

        <!-- 阅读状态 -->
        <div class="filter-group">
          <label>阅读状态</label>
          <el-radio-group
            :model-value="filter.readingStatus"
            @update:model-value="updateFilter('readingStatus', $event)"
          >
            <el-radio value="all">全部</el-radio>
            <el-radio value="unread">未读</el-radio>
            <el-radio value="reading">在读</el-radio>
            <el-radio value="finished">已读完</el-radio>
          </el-radio-group>
        </div>

        <!-- 标签筛选 -->
        <div class="filter-group">
          <label>标签</label>
          <el-select
            :model-value="filter.tags"
            @update:model-value="updateFilter('tags', $event)"
            multiple
            placeholder="选择标签"
            clearable
          >
            <el-option
              v-for="tag in tags"
              :key="tag"
              :label="tag"
              :value="tag"
            />
          </el-select>
        </div>

        <!-- 添加时间范围 -->
        <div class="filter-group">
          <label>添加时间</label>
          <el-date-picker
            :model-value="[filter.dateRange.start, filter.dateRange.end]"
            @update:model-value="updateDateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </div>

        <!-- 操作按钮 -->
        <div class="filter-actions">
          <el-button @click="clearFilter">清空筛选</el-button>
          <el-button type="primary" @click="showFilterDrawer = false">
            确定
          </el-button>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Search, Plus, Grid, List, Filter, SortUp, SortDown, Select,
  Star, View, MoreFilled, InfoFilled, Edit, Delete, Document, Close
} from '@element-plus/icons-vue'
import placeholderImage from '@renderer/assets/placeholder-book.svg'
import { useBookshelfStore } from '@renderer/store/bookshelf'
import BatchOperations from '@renderer/components/BatchOperations.vue'
import type { Book } from '@shared/types/database'
import type { SortField, FilterState } from '@renderer/store/bookshelf'

const router = useRouter()
const bookshelfStore = useBookshelfStore()

// 从store中解构状态和方法
const {
  // 状态
  books,
  loading,
  error,
  viewMode,
  sortField,
  sortOrder,
  filter,
  selectedBooks,
  isSelectionMode,
  categories,
  tags,

  // 计算属性
  paginatedBooks,
  stats,
  pagination,

  // 方法
  loadBooks,
  searchBooks,
  setFilter,
  clearFilter,
  setSort,
  setViewMode,
  setPageSize,
  goToPage,
  toggleBookSelection,
  toggleSelectAll,
  clearSelection,
  toggleSelectionMode,
  setFavorite,
  setBatchFavorite,
  removeBook,
  removeBooks,
  loadCacheData
} = bookshelfStore

// 本地状态
const showFilterDrawer = ref(false)

// 计算属性
const hasActiveFilters = computed(() => {
  return filter.search ||
         filter.author ||
         filter.formats.length > 0 ||
         filter.category ||
         filter.isFavorite !== undefined ||
         filter.readingStatus !== 'all' ||
         filter.tags.length > 0 ||
         filter.dateRange.start ||
         filter.dateRange.end
})

// 获取所有唯一的作者
const uniqueAuthors = computed(() => {
  const authors = new Set<string>()
  books.value.forEach(book => {
    if (book.author && book.author.trim()) {
      authors.add(book.author.trim())
    }
  })
  return Array.from(authors).sort()
})

// 获取所有唯一的出版社
const uniquePublishers = computed(() => {
  const publishers = new Set<string>()
  books.value.forEach(book => {
    if (book.publisher && book.publisher.trim()) {
      publishers.add(book.publisher.trim())
    }
  })
  return Array.from(publishers).sort()
})

// 方法
const handleSearch = (query: string) => {
  searchBooks(query)
}

const handleSortChange = (field: SortField) => {
  setSort(field)
}

const toggleSortOrder = () => {
  setSort(sortField.value)
}

const updateFilter = (key: keyof FilterState, value: any) => {
  setFilter({ [key]: value })
}

const handleFormatChange = (format: string) => {
  if (format === 'all') {
    setFilter({ formats: [] })
  } else {
    setFilter({ formats: [format] })
  }
}

const updateDateRange = (dates: [string, string] | null) => {
  if (dates) {
    setFilter({
      dateRange: {
        start: dates[0],
        end: dates[1]
      }
    })
  } else {
    setFilter({
      dateRange: {}
    })
  }
}

const handleImport = () => {
  router.push('/import')
}

const handleBookClick = (book: Book) => {
  if (isSelectionMode) {
    toggleBookSelection(book.id)
  } else {
    openBook(book)
  }
}

const openBook = (book: Book) => {
  router.push(`/reader/${book.id}`)
}

const handleBookDetail = (book: Book) => {
  // TODO: 显示图书详情弹窗
  console.log('显示图书详情:', book)
}

const handleBookEdit = (book: Book) => {
  // TODO: 显示图书编辑弹窗
  console.log('编辑图书:', book)
}

const handleBookDelete = async (book: Book) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除图书《${book.title}》吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    removeBook(book.id)
    ElMessage.success('删除成功')
  } catch {
    // 用户取消删除
  }
}

const handleBatchDelete = async () => {
  if (selectedBooks.size === 0) {
    ElMessage.warning('请先选择要删除的图书')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedBooks.size} 本图书吗？`,
      '确认批量删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const bookIds = Array.from(selectedBooks)
    removeBooks(bookIds)
    clearSelection()
    ElMessage.success('批量删除成功')
  } catch {
    // 用户取消删除
  }
}

const handleBatchFavorite = async () => {
  if (selectedBooks.size === 0) {
    ElMessage.warning('请先选择要收藏的图书')
    return
  }

  try {
    const bookIds = Array.from(selectedBooks)
    await setBatchFavorite(bookIds, true)
    clearSelection()
    ElMessage.success('批量收藏成功')
  } catch (err) {
    ElMessage.error('批量收藏失败')
  }
}

const getRowClassName = ({ row }: { row: Book }) => {
  return selectedBooks.has(row.id) ? 'selected-row' : ''
}

const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement
  img.src = placeholderImage
}

const formatFileSize = (bytes: number) => {
  if (!bytes) return '0 B'

  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))

  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
}

const formatDate = (dateString?: string) => {
  if (!dateString) return '从未'

  const date = new Date(dateString)
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))

  if (days === 0) return '今天'
  if (days === 1) return '昨天'
  if (days < 7) return `${days}天前`

  return date.toLocaleDateString()
}

const handleBooksUpdated = async () => {
  // 重新加载图书列表以反映更新
  await loadBooks(true)
}

// 监听错误状态
watch(() => bookshelfStore.error, (newError) => {
  if (newError) {
    ElMessage.error(newError)
  }
}, { immediate: false })

// 生命周期
onMounted(async () => {
  try {
    await loadBooks()
    await loadCacheData()
  } catch (err) {
    console.error('初始化图书列表失败:', err)
  }
})
</script>

<style scoped>
.library-view {
  padding: 24px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.library-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 6px;
  gap: 6px;
}

.header-left {
  flex: 1;
}

.page-title {
  font-size: 28px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin: 0 0 8px 0;
}

.stats-info {
  font-size: 14px;
  color: var(--el-text-color-regular);
}

.header-actions {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.search-input {
  width: 100%;
  max-width: 200px;
}

/* 筛选控件样式 */
.filter-controls {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
  padding: 12px 0;
  border-bottom: 1px solid var(--el-border-color-lighter);
}

.filter-controls .el-select {
  min-width: 120px;
}

.filter-controls .el-button {
  margin-left: auto;
}

/* 操作控件样式 */
.action-controls {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
  justify-content: flex-end;
}

.selection-actions {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: var(--el-color-primary-light-9);
  border-radius: 6px;
}

.selection-count {
  font-size: 14px;
  color: var(--el-color-primary);
  font-weight: 500;
}

.library-content {
  flex: 1;
  overflow: auto;
}

.loading-container {
  padding: 20px;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400px;
}

/* 网格视图 */
.books-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 20px;
  padding: 20px 0;
}

.book-card {
  background: var(--el-bg-color-page);
  border: 1px solid var(--el-border-color-light);
  border-radius: 12px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.book-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

.book-card.selected {
  border-color: var(--el-color-primary);
  background: var(--el-color-primary-light-9);
}

.book-checkbox {
  position: absolute;
  top: 12px;
  left: 12px;
  z-index: 2;
}

.favorite-icon {
  position: absolute;
  top: 12px;
  right: 12px;
  z-index: 2;
}

.book-cover {
  width: 100%;
  height: 180px;
  background: var(--el-fill-color-light);
  border-radius: 8px;
  margin-bottom: 12px;
  position: relative;
  overflow: hidden;
}

.book-cover img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.book-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.book-card:hover .book-overlay {
  opacity: 1;
}

.progress-bar {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: rgba(0, 0, 0, 0.1);
}

.progress-fill {
  height: 100%;
  background: var(--el-color-primary);
  transition: width 0.3s ease;
}

.book-info h3 {
  font-size: 16px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin: 0 0 4px 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.book-info p {
  font-size: 14px;
  color: var(--el-text-color-regular);
  margin: 0 0 8px 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.book-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: var(--el-text-color-secondary);
  margin-bottom: 4px;
}

.file-type {
  background: var(--el-color-primary-light-8);
  color: var(--el-color-primary);
  padding: 2px 6px;
  border-radius: 4px;
  font-weight: 500;
}

.read-progress {
  font-size: 12px;
  color: var(--el-color-success);
  font-weight: 500;
}

.book-actions {
  position: absolute;
  top: 12px;
  right: 12px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.book-card:hover .book-actions {
  opacity: 1;
}

/* 列表视图 */
.books-list {
  background: var(--el-bg-color-page);
  border-radius: 12px;
  overflow: hidden;
}

.table-cover {
  width: 40px;
  height: 50px;
  border-radius: 4px;
  overflow: hidden;
}

.table-cover img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}



.book-title-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.title {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.progress-text {
  font-size: 12px;
  color: var(--el-text-color-secondary);
  margin-left: 8px;
}

.selected-row {
  background: var(--el-color-primary-light-9);
}

/* 分页 */
.pagination-container {
  display: flex;
  justify-content: center;
  padding: 20px 0;
}

/* 筛选抽屉 */
.filter-content {
  padding: 20px;
}

.filter-group {
  margin-bottom: 24px;
}

.filter-group label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: var(--el-text-color-primary);
  margin-bottom: 8px;
}

.filter-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  margin-top: 32px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .library-view {
    padding: 16px;
  }

  .library-header {
    flex-direction: column;
    align-items: stretch;
  }

  .header-actions {
    gap: 12px;
  }

  .search-input {
    max-width: 100%;
  }

  .filter-controls {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }

  .filter-controls .el-select {
    width: 100%;
  }

  .action-controls {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }

  .books-grid {
    grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
    gap: 16px;
  }

  .selection-actions {
    flex-wrap: wrap;
    gap: 6px;
  }
}
</style>
