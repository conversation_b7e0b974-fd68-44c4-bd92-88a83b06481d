import{d as c,a as l,f as e,w as o,h as n,g as u,v as d,x as p,_ as i}from"./index-N2UppB4L.js";const m={class:"not-found"},f=c({__name:"NotFound",setup(x){const s=d(),a=()=>{s.push("/library")};return(b,t)=>{const r=n("el-button"),_=n("el-result");return p(),l("div",m,[e(_,{icon:"warning",title:"404","sub-title":"抱歉，您访问的页面不存在"},{extra:o(()=>[e(r,{type:"primary",onClick:a},{default:o(()=>t[0]||(t[0]=[u("返回首页")])),_:1,__:[0]})]),_:1})])}}}),C=i(f,[["__scopeId","data-v-9acdf3bd"]]);export{C as default};
